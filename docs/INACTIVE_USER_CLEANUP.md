# Inactive User Cleanup System

This document explains how IsotopeAI automatically deletes inactive user accounts and their data after 3 months of inactivity.

## Overview

To maintain database performance and reduce storage costs, IsotopeAI automatically cleans up user accounts that have been inactive for more than 3 months. This system:

1. Tracks user activity across the application
2. Issues warnings to users approaching the inactivity threshold
3. Provides an admin interface for manual cleanup
4. Automatically deletes inactive accounts and their associated data

## How It Works

### Activity Tracking

- Each time a user interacts with the application (login, page navigation, clicks, etc.), their `lastActive` timestamp is updated in their user profile
- This timestamp is used to determine when a user was last active on the platform
- Activity tracking is implemented using the `useActivityTracker` hook and integrated into the authentication system

### Inactivity Checks

When users log in, the system:
1. Checks their last active timestamp
2. Updates their current active status
3. Shows appropriate warnings if they've been inactive for an extended period

### Cleanup Process

Inactive accounts are handled in the following way:

1. **Warning at 2.5 months**: Users inactive for 2.5 months receive a notification warning them that their account will be deleted soon
2. **Final warning at 2.75 months**: Users receive a final warning notification
3. **Deletion at 3 months**: After 3 months of inactivity, accounts are eligible for deletion

### Data Removed During Cleanup

When an inactive account is deleted, the following data is removed:

- User profile and authentication data
- FCM notification tokens
- User chat history and messages
- Shared chat posts
- All associated user-generated content

## Implementation Details

Since IsotopeAI uses Firebase's free tier (Spark plan), which doesn't support scheduled cloud functions, we've implemented a hybrid solution:

1. **Client-side tracking**: Activity is tracked whenever users interact with the app
2. **Login-time checks**: Inactivity checks happen when users log in
3. **Admin panel**: Administrators can manually trigger cleanup for inactive users

## Admin Interface

Administrators can access cleanup tools through:

1. Navigate to Settings > Admin tab
2. Use the "Inactive User Cleanup" tool
3. View statistics on processed and deleted accounts

## Notes

- The 3-month inactivity period provides ample time for users to return to the platform
- Warning notifications help prevent accidental deletion of accounts users still wish to maintain
- You may consider upgrading to Firebase Blaze (pay-as-you-go) plan to implement fully automated server-side deletion using scheduled functions 