<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C823RR97DM"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-C823RR97DM');
    </script>
    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9602732057654649"
     crossorigin="anonymous"></script>
    <meta charset="UTF-8" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="google-site-verification" content="kv7PyTeC5eMUIW_qepJUof-eaU2B3OySFljnrqLfr04" />
    
    <!-- Enhanced Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' https://cdn.gpteng.co https://static.cloudflareinsights.com https://do.featurebase.app https://www.googletagmanager.com https://www.desmos.com https://cdn.jsdelivr.net https://pagead2.googlesyndication.com 'unsafe-inline'; style-src 'self' https://fonts.googleapis.com 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.googleapis.com https://*.firebaseio.com https://*.supabase.co; frame-src 'self' https://googleads.g.doubleclick.net;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Anta&display=swap" rel="stylesheet">
    
    <meta
      name="description"
      content="IsotopeAI is an AI-powered platform designed to solve your PCMB doubts. Get instant help with Physics, Chemistry, and Mathematics problems using advanced AI technology."
    />
    <meta
      name="keywords"
      content="IsotopeAI, AI doubt solver, JEE doubt solver, physics doubts, chemistry doubts, mathematics doubts, PCMB tutor, AI tutor, JEE preparation, NEET preparation, online doubt solving"
    />
    <title>IsotopeAI - AI-Powered Physics, Chemistry & Mathematics Doubt Solver</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#166534" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="IsotopeAI" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icon-192x192.png" />

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="author" content="IsotopeAI" />
    <meta name="language" content="English" />
    <link rel="canonical" href="https://isotopeai.in/" />

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="IsotopeAI - AI-Powered Physics, Chemistry & Mathematics Doubt Solver" />
    <meta
      property="og:description"
      content="IsotopeAI is an AI-powered platform designed to solve your PCMB doubts. Get instant help with Physics, Chemistry, and Mathematics problems using advanced AI technology."
    />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:url" content="https://isotopeai.in/" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="IsotopeAI" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="IsotopeAI - AI-Powered Physics, Chemistry & Mathematics Doubt Solver" />
    <meta name="twitter:description" content="Get instant help with Physics, Chemistry, and Mathematics problems using advanced AI technology." />
    <meta name="twitter:image" content="/og-image.png" />
    <meta name="twitter:url" content="https://isotopeai.in" />

    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "IsotopeAI",
      "description": "IsotopeAI is an AI-powered platform designed to solve your PCMB doubts. Get instant help with Physics, Chemistry, and Mathematics problems using advanced AI technology.",
      "url": "https://isotopeai.in/",
      "applicationCategory": "EducationalApplication",
      "offers": {
        "@type": "Offer",
        "price": "0"
      },
      "operatingSystem": "Any"
    }
    </script>
    <script async src="https://fundingchoicesmessages.google.com/i/pub-9602732057654649?ers=1"></script><script>(function() {function signalGooglefcPresent() {if (!window.frames['googlefcPresent']) {if (document.body) {const iframe = document.createElement('iframe'); iframe.style = 'width: 0; height: 0; border: none; z-index: -1000; left: -1000px; top: -1000px;'; iframe.style.display = 'none'; iframe.name = 'googlefcPresent'; document.body.appendChild(iframe);} else {setTimeout(signalGooglefcPresent, 0);}}}signalGooglefcPresent();})();</script>
  </head>
  <body>
    <div id="root"></div>
    
    <!-- Script to detect and block suspicious requests -->
    <script>
      // Function to monitor and block suspicious network requests
      (function() {
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest.prototype.open;
        
        // Monitor and potentially block fetch requests
        window.fetch = function() {
          const url = arguments[0];
          let urlString = '';
          
          if (typeof url === 'string') {
            urlString = url;
          } else if (url instanceof Request) {
            urlString = url.url;
          }
          
          // Check for suspicious URLs
          if (urlString.includes('fywiei.com') || 
              urlString.includes('jr.php') || 
              /\.php\?gz=/.test(urlString)) {
            console.warn('Blocked suspicious request to:', urlString);
            return Promise.reject(new Error('Request blocked for security reasons'));
          }
          
          return originalFetch.apply(this, arguments);
        };
        
        // Monitor and potentially block XHR requests
        window.XMLHttpRequest.prototype.open = function() {
          const url = arguments[1];
          
          // Check for suspicious URLs
          if (url.includes('fywiei.com') || 
              url.includes('jr.php') || 
              /\.php\?gz=/.test(url)) {
            console.warn('Blocked suspicious XHR request to:', url);
            throw new Error('Request blocked for security reasons');
          }
          
          return originalXHR.apply(this, arguments);
        };
      })();
    </script>
    
    <!-- Load scripts with integrity checks where possible -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module" onerror="console.error('Failed to load GPT Engineer script')"></script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('ServiceWorker registration successful');
            })
            .catch((err) => {
              console.log('ServiceWorker registration failed: ', err);
            });
        });
      }
    </script>
    
    <!-- Cloudflare Web Analytics with error handling -->
    <script defer 
      src='https://static.cloudflareinsights.com/beacon.min.js' 
      data-cf-beacon='{"token": "7a43d7e470764db4a5f24947a91652a6"}'
      onerror="console.error('Failed to load Cloudflare Insights')"></script>

    <!-- Featurebase SDK with error handling -->
    <script>
      !(function (e, t) {
        const a = "featurebase-sdk";
        function n() {
          if (!t.getElementById(a)) {
            var e = t.createElement("script");
            (e.id = a),
            (e.src = "https://do.featurebase.app/js/sdk.js"),
            (e.onerror = function() { console.error('Failed to load Featurebase SDK'); }),
            t.getElementsByTagName("script")[0].parentNode.insertBefore(
              e,
              t.getElementsByTagName("script")[0]
            );
          }
        }
        "function" != typeof e.Featurebase &&
          (e.Featurebase = function () {
            (e.Featurebase.q = e.Featurebase.q || []).push(arguments);
          }),
        "complete" === t.readyState || "interactive" === t.readyState
          ? n()
          : t.addEventListener("DOMContentLoaded", n);
      })(window, document);
    </script>

    <!-- Featurebase User Identification -->
    <script>
      // Function to identify user in Featurebase
      function identifyFeaturebaseUser(userData) {
        if (typeof Featurebase === 'function') {
          Featurebase(
            "identify",
            {
              organization: "isotopeai", // Replace with your actual organization name
              ...userData,
              // Optional callback function
            },
            (err) => {
              if (err) {
                console.error("Featurebase identification error:", err);
              } else {
                console.log("Featurebase identification successful");
              }
            }
          );
        }
      }
    </script>
  </body>
</html>
