import * as functions from "firebase-functions";
import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK
// Ensure your service account key is set up in your environment
// (e.g., GOOGLE_APPLICATION_CREDENTIALS environment variable)
try {
  admin.initializeApp();
  console.log("Firebase Admin SDK initialized successfully.");
} catch (error) {
  console.error("Error initializing Firebase Admin SDK:", error);
  // Handle initialization error appropriately
}

const db = admin.firestore();
const messaging = admin.messaging();

// Example function (replace or remove later)
export const helloWorld = functions.https.onRequest((request, response) => {
  functions.logger.info("Hello logs!", {structuredData: true});
  response.send("Hello from Firebase!");
});

// --- FCM Token Management ---

// Function to store FCM token for a user
export const storeFcmToken = functions.https.onCall(async (data, context) => {
  // Check authentication
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "The function must be called while authenticated."
    );
  }

  const userId = context.auth.uid;
  const token = data.token;

  if (!token || typeof token !== "string") {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "The function must be called with a valid 'token' argument."
    );
  }

  try {
    const userRef = db.collection("users").doc(userId);
    // Store the token in a subcollection or an array field
    // Using a subcollection is generally more scalable if users can have multiple tokens
    const tokensRef = userRef.collection("fcmTokens").doc(token);
    await tokensRef.set({
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      // You might want to store other info like device type, last used time, etc.
    });

    functions.logger.info(`Stored FCM token for user ${userId}`);
    return { success: true };
  } catch (error) {
    functions.logger.error(`Error storing FCM token for user ${userId}:`, error);
    throw new functions.https.HttpsError(
      "internal",
      "Could not store FCM token."
    );
  }
});

// --- Notification Triggers ---

// Function to send notification when a user is mentioned in a comment/reply
export const sendMentionNotification = functions.firestore
  .document("chats/{chatId}/comments/{commentId}")
  .onCreate(async (snapshot, context) => {
    const commentData = snapshot.data();
    const chatId = context.params.chatId;
    const commentId = context.params.commentId; // Useful for linking later

    if (!commentData) {
      functions.logger.error("No data associated with the event");
      return;
    }

    const mentionedUserIds: string[] | undefined = commentData.mentionedUserIds;
    const authorId: string = commentData.authorId;
    const authorUsername: string = commentData.author || "Someone"; // Fallback username

    if (!mentionedUserIds || mentionedUserIds.length === 0) {
      functions.logger.log("No mentions found in comment:", snapshot.id);
      return; // No mentions, exit function
    }

    functions.logger.log(`Mentions found for users: ${mentionedUserIds.join(", ")} by ${authorUsername} (${authorId})`);

    // Prepare notification payload
    const notificationPayload = {
      notification: {
        title: "New Mention",
        body: `${authorUsername} mentioned you in a discussion.`,
        // icon: "/favicon.ico", // Optional: Client-side SW can handle icon
        // click_action: `/chat/${chatId}` // Optional: Define click action URL (handle on client)
      },
      // Add custom data if needed for client-side handling
      data: {
        chatId: chatId,
        commentId: commentId, // Send comment ID for potential deep linking
        type: "mention", // Custom type to identify notification
      },
    };

    // Get tokens for each mentioned user and send notifications
    const tokenPromises = mentionedUserIds.map(async (userId) => {
      // Don't notify the user if they mentioned themselves
      if (userId === authorId) {
        return;
      }

      const tokensSnapshot = await db
        .collection("users")
        .doc(userId)
        .collection("fcmTokens")
        .get();

      if (tokensSnapshot.empty) {
        functions.logger.log(`No FCM tokens found for mentioned user: ${userId}`);
        return;
      }

      const tokens = tokensSnapshot.docs.map((doc) => doc.id);
      functions.logger.log(`Found tokens for user ${userId}:`, tokens);

      // Send notification to all tokens for the user
      const response = await messaging.sendToDevice(tokens, notificationPayload);

      // Cleanup stale tokens
      const tokensToRemove: Promise<any>[] = [];
      response.results.forEach((result, index) => {
        const error = result.error;
        if (error) {
          functions.logger.error(
            `Failure sending notification to token ${tokens[index]} for user ${userId}`,
            error
          );
          // Cleanup the tokens who are not registered anymore.
          if (
            error.code === "messaging/invalid-registration-token" ||
            error.code === "messaging/registration-token-not-registered"
          ) {
            tokensToRemove.push(
              db
                .collection("users")
                .doc(userId)
                .collection("fcmTokens")
                .doc(tokens[index])
                .delete()
            );
          }
        }
      });
      return Promise.all(tokensToRemove);
    });

    try {
      await Promise.all(tokenPromises);
      functions.logger.log("Mention notifications sent successfully.");
    } catch (error) {
      functions.logger.error("Error processing mention notifications:", error);
    }
  });

// --- Task Notifications ---

// Function to send notification when a task is assigned
export const sendTaskAssignmentNotification = functions.firestore
  .document("todos/{taskId}")
  .onWrite(async (change, context) => {
    const taskId = context.params.taskId;
    const beforeData = change.before.data();
    const afterData = change.after.data();

    // Exit if task was deleted or data is missing
    if (!afterData) {
      functions.logger.log(`Task ${taskId} deleted or no data after write.`);
      return;
    }

    const assignedToUserId: string | undefined = afterData.assignedTo;
    const previousAssignedToUserId: string | undefined = beforeData?.assignedTo;
    const taskTitle: string = afterData.title || "a task";
    const assignerId: string = afterData.createdBy; // Assuming creator assigns initially

    // Check if assignedTo changed or was newly set, and it's not self-assignment
    if (
      assignedToUserId &&
      assignedToUserId !== assignerId && // Don't notify if assigning to self
      assignedToUserId !== previousAssignedToUserId // Only notify on change or initial assignment
    ) {
      functions.logger.log(`Task ${taskId} assigned to user ${assignedToUserId}. Sending notification.`);

      // Get assigner's username (optional, for better notification text)
      let assignerUsername = "Someone";
      try {
        const assignerDoc = await db.collection("users").doc(assignerId).get();
        if (assignerDoc.exists) {
          assignerUsername = assignerDoc.data()?.username || assignerId;
        }
      } catch (error) {
        functions.logger.error(`Error fetching assigner profile ${assignerId}:`, error);
      }

      // Prepare notification
      const notificationPayload = {
        notification: {
          title: "New Task Assigned",
          body: `${assignerUsername} assigned you a task: ${taskTitle}`,
        },
        data: {
          taskId: taskId,
          type: "task_assigned",
        },
      };

      // Get tokens and send
      const tokensSnapshot = await db
        .collection("users")
        .doc(assignedToUserId)
        .collection("fcmTokens")
        .get();

      if (tokensSnapshot.empty) {
        functions.logger.log(`No FCM tokens found for assigned user: ${assignedToUserId}`);
        return;
      }

      const tokens = tokensSnapshot.docs.map((doc) => doc.id);
      const response = await messaging.sendToDevice(tokens, notificationPayload);

      // Cleanup stale tokens (similar logic as in sendMentionNotification)
      const tokensToRemove: Promise<any>[] = [];
      response.results.forEach((result, index) => {
        const error = result.error;
        if (error) {
          functions.logger.error(`Failure sending task assignment notification to token ${tokens[index]} for user ${assignedToUserId}`, error);
          if (error.code === "messaging/invalid-registration-token" || error.code === "messaging/registration-token-not-registered") {
            tokensToRemove.push(db.collection("users").doc(assignedToUserId).collection("fcmTokens").doc(tokens[index]).delete());
          }
        }
      });
      await Promise.all(tokensToRemove);
      functions.logger.log(`Task assignment notification sent to user ${assignedToUserId}.`);
    } else {
      functions.logger.log(`No assignment notification needed for task ${taskId}. AssignedTo: ${assignedToUserId}, Previous: ${previousAssignedToUserId}, Assigner: ${assignerId}`);
    }
  });

// Function to send task due date reminders (Scheduled)
// Schedule to run, e.g., every day at 8 AM
export const sendTaskDueDateReminders = functions.pubsub
  .schedule("every day 08:00")
  // .timeZone("Asia/Calcutta") // Optional: Specify timezone
  .onRun(async (context) => {
    functions.logger.log("Running scheduled task due date reminder check...");

    const now = admin.firestore.Timestamp.now();
    // Calculate timestamp for 24 hours from now
    const tomorrow = admin.firestore.Timestamp.fromMillis(now.toMillis() + 24 * 60 * 60 * 1000);

    // Query for tasks due within the next 24 hours that haven't been completed
    // Assuming 'done' tasks are moved to a specific column or have a 'completed' flag
    // Let's assume tasks not in 'column-3' are not done. Adjust if using a flag.
    const tasksDueSoonQuery = db.collection("todos")
      .where("dueDate", ">=", now)
      .where("dueDate", "<=", tomorrow)
      .where("columnId", "!=", "column-3"); // Example: Filter out 'Done' tasks

    try {
      const snapshot = await tasksDueSoonQuery.get();
      if (snapshot.empty) {
        functions.logger.log("No tasks due soon.");
        return null;
      }

      functions.logger.log(`Found ${snapshot.size} tasks due soon.`);

      // Process each task and send notifications
      const notificationPromises = snapshot.docs.map(async (doc) => {
        const task = doc.data();
        const taskId = doc.id;
        const assignedToUserId: string | undefined = task.assignedTo;
        const taskTitle: string = task.title || "a task";

        if (!assignedToUserId) {
          functions.logger.log(`Task ${taskId} has no assignee.`);
          return; // Skip if no one is assigned
        }

        // Prepare notification
        const dueDate = (task.dueDate as admin.firestore.Timestamp).toDate();
        const formattedDueDate = dueDate.toLocaleDateString("en-US", { month: "short", day: "numeric" });
        const notificationPayload = {
          notification: {
            title: "Task Due Soon",
            body: `Reminder: Task "${taskTitle}" is due on ${formattedDueDate}.`,
          },
          data: {
            taskId: taskId,
            type: "task_due_reminder",
          },
        };

        // Get tokens and send
        const tokensSnapshot = await db
          .collection("users")
          .doc(assignedToUserId)
          .collection("fcmTokens")
          .get();

        if (tokensSnapshot.empty) {
          functions.logger.log(`No FCM tokens found for user ${assignedToUserId} for task ${taskId} reminder.`);
          return;
        }

        const tokens = tokensSnapshot.docs.map((doc) => doc.id);
        const response = await messaging.sendToDevice(tokens, notificationPayload);

        // Cleanup stale tokens
        const tokensToRemove: Promise<any>[] = [];
        response.results.forEach((result, index) => {
          const error = result.error;
          if (error) {
            functions.logger.error(`Failure sending due date reminder to token ${tokens[index]} for user ${assignedToUserId}`, error);
            if (error.code === "messaging/invalid-registration-token" || error.code === "messaging/registration-token-not-registered") {
              tokensToRemove.push(db.collection("users").doc(assignedToUserId).collection("fcmTokens").doc(tokens[index]).delete());
            }
          }
        });
        await Promise.all(tokensToRemove);
        functions.logger.log(`Due date reminder sent for task ${taskId} to user ${assignedToUserId}.`);
      });

      await Promise.all(notificationPromises);
      functions.logger.log("Finished sending task due date reminders.");

    } catch (error) {
      functions.logger.error("Error querying or sending task due date reminders:", error);
    }
    return null; // Required for scheduled functions
  });

// --- Group Chat Notifications ---

// Function to send notification when a new message is posted in a group
export const sendGroupMessageNotification = functions.firestore
  .document("groups/{groupId}/messages/{messageId}")
  .onCreate(async (snapshot, context) => {
    const messageData = snapshot.data();
    const groupId = context.params.groupId;
    // const messageId = context.params.messageId; // Not strictly needed for notification

    if (!messageData) {
      functions.logger.error("No data associated with the new message event");
      return;
    }

    const senderId: string = messageData.senderId;
    const messageContent: string = messageData.content || "sent a message"; // Fallback content
    const senderUsername: string = messageData.senderUsername || "Someone"; // Assuming senderUsername is stored

    // 1. Get group details (members and name)
    let groupMembers: string[] = [];
    let groupName = "a group";
    try {
      const groupDoc = await db.collection("groups").doc(groupId).get();
      if (groupDoc.exists) {
        groupMembers = groupDoc.data()?.members || [];
        groupName = groupDoc.data()?.name || groupName;
      } else {
        functions.logger.error(`Group document ${groupId} not found.`);
        return; // Cannot proceed without group info
      }
    } catch (error) {
      functions.logger.error(`Error fetching group ${groupId}:`, error);
      return;
    }

    if (groupMembers.length === 0) {
      functions.logger.log(`Group ${groupId} has no members.`);
      return;
    }

    functions.logger.log(`New message in group ${groupName} (${groupId}) by ${senderUsername} (${senderId}). Notifying members: ${groupMembers.join(", ")}`);

    // 2. Prepare notification payload
    const notificationPayload = {
      notification: {
        title: `New Message in ${groupName}`,
        body: `${senderUsername}: ${messageContent.substring(0, 100)}${messageContent.length > 100 ? "..." : ""}`, // Truncate long messages
      },
      data: {
        groupId: groupId,
        // messageId: messageId, // Optional
        type: "group_message",
      },
    };

    // 3. Get tokens for each member (excluding sender) and send notifications
    const tokenPromises = groupMembers.map(async (memberId) => {
      // Don't notify the sender
      if (memberId === senderId) {
        return;
      }

      const tokensSnapshot = await db
        .collection("users")
        .doc(memberId)
        .collection("fcmTokens")
        .get();

      if (tokensSnapshot.empty) {
        functions.logger.log(`No FCM tokens found for group member: ${memberId}`);
        return;
      }

      const tokens = tokensSnapshot.docs.map((doc) => doc.id);
      functions.logger.log(`Found tokens for member ${memberId}:`, tokens);

      // Send notification
      const response = await messaging.sendToDevice(tokens, notificationPayload);

      // Cleanup stale tokens
      const tokensToRemove: Promise<any>[] = [];
      response.results.forEach((result, index) => {
        const error = result.error;
        if (error) {
          functions.logger.error(`Failure sending group message notification to token ${tokens[index]} for user ${memberId}`, error);
          if (error.code === "messaging/invalid-registration-token" || error.code === "messaging/registration-token-not-registered") {
            tokensToRemove.push(db.collection("users").doc(memberId).collection("fcmTokens").doc(tokens[index]).delete());
          }
        }
      });
      return Promise.all(tokensToRemove);
    });

    try {
      await Promise.all(tokenPromises);
      functions.logger.log(`Group message notifications sent for group ${groupId}.`);
    } catch (error) {
      functions.logger.error(`Error processing group message notifications for group ${groupId}:`, error);
    }
  });

// --- Scheduled Analytics Reminder ---

// Schedule to run, e.g., every day at 10 PM (22:00)
export const sendAnalyticsReminder = functions.pubsub
  .schedule("every day 22:00")
  // .timeZone("Asia/Calcutta") // Optional: Specify timezone relevant to users
  .onRun(async (context) => {
    functions.logger.log("Running scheduled analytics reminder check...");

    // Prepare notification payload
    const notificationPayload = {
      notification: {
        title: "Analytics Check-in",
        body: "Don't forget to check your study analytics for today!",
      },
      data: {
        type: "analytics_reminder",
        // Add link data if needed, e.g., targetPath: "/analytics"
      },
    };

    try {
      // Get all users (consider pagination for large user bases)
      const usersSnapshot = await db.collection("users").get();
      if (usersSnapshot.empty) {
        functions.logger.log("No users found to send analytics reminder.");
        return null;
      }

      functions.logger.log(`Found ${usersSnapshot.size} users. Sending reminders...`);

      // Process each user and send notifications
      const notificationPromises = usersSnapshot.docs.map(async (userDoc) => {
        const userId = userDoc.id;

        // Get tokens for the user
        const tokensSnapshot = await db
          .collection("users")
          .doc(userId)
          .collection("fcmTokens")
          .get();

        if (tokensSnapshot.empty) {
          functions.logger.log(`No FCM tokens found for user ${userId} for analytics reminder.`);
          return; // Skip if no tokens
        }

        const tokens = tokensSnapshot.docs.map((doc) => doc.id);
        functions.logger.log(`Found tokens for user ${userId}:`, tokens);

        // Send notification
        const response = await messaging.sendToDevice(tokens, notificationPayload);

        // Cleanup stale tokens
        const tokensToRemove: Promise<any>[] = [];
        response.results.forEach((result, index) => {
          const error = result.error;
          if (error) {
            functions.logger.error(`Failure sending analytics reminder to token ${tokens[index]} for user ${userId}`, error);
            if (error.code === "messaging/invalid-registration-token" || error.code === "messaging/registration-token-not-registered") {
              tokensToRemove.push(db.collection("users").doc(userId).collection("fcmTokens").doc(tokens[index]).delete());
            }
          }
        });
        await Promise.all(tokensToRemove);
        functions.logger.log(`Analytics reminder sent to user ${userId}.`);
      });

      await Promise.all(notificationPromises);
      functions.logger.log("Finished sending analytics reminders.");

    } catch (error) {
      functions.logger.error("Error querying or sending analytics reminders:", error);
    }
    return null; // Required for scheduled functions
  });

// --- Inactive User Cleanup ---

// Scheduled function to notify users approaching inactivity threshold (runs weekly)
export const notifyInactiveUsers = functions.pubsub
  .schedule("every monday 01:00")
  .timeZone("America/New_York") // Adjust timezone as needed
  .onRun(async (context) => {
    functions.logger.log("Running inactive user notification check...");

    const now = admin.firestore.Timestamp.now();
    // Calculate timestamp for 2.5 months ago (warning before 3-month deletion)
    const twoAndHalfMonthsAgo = admin.firestore.Timestamp.fromMillis(
      now.toMillis() - 75 * 24 * 60 * 60 * 1000
    );
    
    // Calculate timestamp for 2.75 months ago (final warning)
    const twoAndThreeQuarterMonthsAgo = admin.firestore.Timestamp.fromMillis(
      now.toMillis() - 83 * 24 * 60 * 60 * 1000
    );

    try {
      // Get all users
      const usersSnapshot = await db.collection("users").get();
      let firstWarningCount = 0;
      let finalWarningCount = 0;
      
      functions.logger.log(`Processing ${usersSnapshot.size} users for inactivity notifications...`);
      
      const notificationPromises = usersSnapshot.docs.map(async (userDoc) => {
        const userData = userDoc.data();
        const userId = userDoc.id;
        
        // Check last activity using either lastActive from stats or lastLogin
        // Fallback to createdAt if neither exists
        const lastActiveTimestamp = 
          userData.stats?.lastActive || 
          userData.lastLogin || 
          userData.createdAt || 
          null;
        
        if (!lastActiveTimestamp) {
          return; // Skip if no timestamp available
        }
        
        // Convert string timestamp to Date object if necessary
        let lastActiveDate;
        if (lastActiveTimestamp instanceof admin.firestore.Timestamp) {
          lastActiveDate = lastActiveTimestamp.toDate();
        } else {
          lastActiveDate = new Date(lastActiveTimestamp);
        }
        
        // Get user's email from auth service if not in Firestore
        let userEmail = userData.email;
        if (!userEmail) {
          try {
            const authUser = await admin.auth().getUser(userId);
            userEmail = authUser.email;
          } catch (error) {
            functions.logger.error(`Error fetching auth user ${userId}:`, error);
            return; // Skip if can't get email
          }
        }
        
        if (!userEmail) {
          functions.logger.warn(`User ${userId} has no email address, skipping notification`);
          return;
        }
        
        // Check if between 2.5 and 2.75 months inactive (first warning)
        if (lastActiveDate.getTime() < twoAndHalfMonthsAgo.toMillis() && 
            lastActiveDate.getTime() >= twoAndThreeQuarterMonthsAgo.toMillis()) {
          
          functions.logger.log(`Sending first inactivity warning to user ${userId}`);
          
          // Get tokens for the user for FCM notification
          const tokensSnapshot = await db
            .collection("users")
            .doc(userId)
            .collection("fcmTokens")
            .get();
          
          if (!tokensSnapshot.empty) {
            const tokens = tokensSnapshot.docs.map(doc => doc.id);
            
            // Prepare first warning notification
            const notificationPayload = {
              notification: {
                title: "Account Inactivity Notice",
                body: "Your account has been inactive for over 2.5 months. Log in within 2 weeks to prevent automatic deletion.",
              },
              data: {
                type: "inactivity_warning",
                severity: "first_warning"
              },
            };
            
            await messaging.sendToDevice(tokens, notificationPayload);
            firstWarningCount++;
          }
          
          // Also send email if possible (would require a separate email service)
          // For example: await sendInactivityWarningEmail(userEmail, "first_warning");
        }
        // Check if between 2.75 and 3 months inactive (final warning)
        else if (lastActiveDate.getTime() < twoAndThreeQuarterMonthsAgo.toMillis()) {
          
          functions.logger.log(`Sending final inactivity warning to user ${userId}`);
          
          // Get tokens for the user for FCM notification
          const tokensSnapshot = await db
            .collection("users")
            .doc(userId)
            .collection("fcmTokens")
            .get();
          
          if (!tokensSnapshot.empty) {
            const tokens = tokensSnapshot.docs.map(doc => doc.id);
            
            // Prepare final warning notification
            const notificationPayload = {
              notification: {
                title: "FINAL NOTICE: Account Deletion Imminent",
                body: "Your account will be deleted in 1 week due to inactivity. Log in now to prevent data loss.",
              },
              data: {
                type: "inactivity_warning",
                severity: "final_warning"
              },
            };
            
            await messaging.sendToDevice(tokens, notificationPayload);
            finalWarningCount++;
          }
          
          // Also send email if possible (would require a separate email service)
          // For example: await sendInactivityWarningEmail(userEmail, "final_warning");
        }
      });
      
      await Promise.all(notificationPromises);
      functions.logger.log(`Inactive user notifications complete. Sent ${firstWarningCount} first warnings and ${finalWarningCount} final warnings.`);
    } catch (error) {
      functions.logger.error("Error in inactive user notifications:", error);
    }
    
    return null; // Required for scheduled functions
  });

// Scheduled function to delete inactive users (runs weekly)
export const cleanupInactiveUsers = functions.pubsub
  .schedule("every monday 03:00")
  .timeZone("America/New_York") // Adjust timezone as needed
  .onRun(async (context) => {
    functions.logger.log("Running inactive user cleanup...");

    const now = admin.firestore.Timestamp.now();
    // Calculate timestamp for 3 months ago
    const threeMonthsAgo = admin.firestore.Timestamp.fromMillis(
      now.toMillis() - 90 * 24 * 60 * 60 * 1000
    );

    try {
      // Get all users
      const usersSnapshot = await db.collection("users").get();
      let deletedCount = 0;
      let processedCount = 0;
      
      functions.logger.log(`Processing ${usersSnapshot.size} users for inactivity check...`);
      
      const deletePromises = usersSnapshot.docs.map(async (userDoc) => {
        const userData = userDoc.data();
        const userId = userDoc.id;
        processedCount++;
        
        // Check last activity using either lastActive from stats or lastLogin
        // Fallback to createdAt if neither exists
        const lastActiveTimestamp = 
          userData.stats?.lastActive || 
          userData.lastLogin || 
          userData.createdAt || 
          null;
        
        if (!lastActiveTimestamp) {
          functions.logger.warn(`User ${userId} has no activity timestamps, skipping`);
          return;
        }
        
        // Convert string timestamp to Date object if necessary
        let lastActiveDate;
        if (lastActiveTimestamp instanceof admin.firestore.Timestamp) {
          lastActiveDate = lastActiveTimestamp.toDate();
        } else {
          lastActiveDate = new Date(lastActiveTimestamp);
        }
        
        // Check if user has been inactive for 3 months
        if (lastActiveDate.getTime() < threeMonthsAgo.toMillis()) {
          functions.logger.log(`User ${userId} inactive since ${lastActiveDate}, deleting...`);
          
          // Batch to collect all delete operations
          const batch = db.batch();
          
          try {
            // 1. Get all FCM tokens for this user and delete them
            const tokensSnapshot = await db
              .collection("users")
              .doc(userId)
              .collection("fcmTokens")
              .get();
            
            tokensSnapshot.docs.forEach(tokenDoc => {
              batch.delete(tokenDoc.ref);
            });
            
            // 2. Delete user's chat history
            const userChatsSnapshot = await db
              .collection("user_chats")
              .where("userId", "==", userId)
              .get();
            
            userChatsSnapshot.docs.forEach(chatDoc => {
              batch.delete(chatDoc.ref);
            });
            
            // 3. Delete user's shared chats
            const sharedChatsSnapshot = await db
              .collection("shared_chats")
              .where("userId", "==", userId)
              .get();
            
            sharedChatsSnapshot.docs.forEach(chatDoc => {
              batch.delete(chatDoc.ref);
            });
            
            // 4. Delete user document itself
            batch.delete(userDoc.ref);
            
            // 5. Execute batch delete
            await batch.commit();
            
            // 6. Delete user auth account (separate from Firestore)
            await admin.auth().deleteUser(userId);
            
            deletedCount++;
            functions.logger.log(`Successfully deleted inactive user ${userId} and their data`);
          } catch (error) {
            functions.logger.error(`Error deleting user ${userId}:`, error);
          }
        }
      });
      
      await Promise.all(deletePromises);
      functions.logger.log(`Inactive user cleanup complete. Processed ${processedCount} users, deleted ${deletedCount} inactive users.`);
    } catch (error) {
      functions.logger.error("Error in inactive user cleanup:", error);
    }
    
    return null; // Required for scheduled functions
  });
