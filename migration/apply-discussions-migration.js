import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables from parent directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
config({ path: path.join(__dirname, '..', '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyDiscussionsMigration() {
  try {
    console.log('🚀 Starting discussions table migration...');

    // Read the SQL file
    const sqlPath = path.join(__dirname, 'discussions-table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error);
          // Continue with other statements
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err.message);
        // Continue with other statements
      }
    }
    
    console.log('🎉 Migration completed!');
    
    // Test the new table
    console.log('🧪 Testing discussions table...');
    const { data, error } = await supabase
      .from('discussions')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      console.error('❌ Error testing discussions table:', error);
    } else {
      console.log('✅ Discussions table is accessible');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Alternative approach using direct table access
async function applyDiscussionsMigrationDirect() {
  try {
    console.log('🚀 Starting discussions table migration (direct approach)...');

    // Test if discussions table already exists
    console.log('🔍 Checking if discussions table exists...');
    const { data: existingData, error: existingError } = await supabase
      .from('discussions')
      .select('*')
      .limit(1);

    if (!existingError) {
      console.log('✅ Discussions table already exists and is accessible');
      console.log('🧪 Testing table functionality...');

      // Test the table
      const { data, error } = await supabase
        .from('discussions')
        .select('*')
        .limit(1);

      if (error) {
        console.error('❌ Error testing discussions table:', error);
      } else {
        console.log('✅ Discussions table is working correctly');
      }

      console.log('🎉 Migration completed successfully!');
      return;
    }

    console.log('⚠️ Discussions table does not exist or is not accessible');
    console.log('📋 Please create the discussions table manually in Supabase dashboard');
    console.log('');
    console.log('SQL to run in Supabase SQL Editor:');
    console.log('=====================================');

    const createTableSQL = `
-- Create discussions table for AI chat discussions
CREATE TABLE IF NOT EXISTS discussions (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    chat_id TEXT NOT NULL,
    author_id TEXT NOT NULL,
    author_name TEXT,
    author_username TEXT,
    author_photo_url TEXT,
    content TEXT NOT NULL,
    parent_id TEXT REFERENCES discussions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_discussions_chat_id ON discussions(chat_id);
CREATE INDEX IF NOT EXISTS idx_discussions_parent_id ON discussions(parent_id);
CREATE INDEX IF NOT EXISTS idx_discussions_author_id ON discussions(author_id);
CREATE INDEX IF NOT EXISTS idx_discussions_created_at ON discussions(created_at DESC);

-- Enable Row Level Security
ALTER TABLE discussions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for discussions (public read, authenticated write)
CREATE POLICY "Discussions are viewable by everyone" ON discussions FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert discussions" ON discussions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update their own discussions" ON discussions FOR UPDATE USING (auth.uid()::text = author_id);
CREATE POLICY "Users can delete their own discussions" ON discussions FOR DELETE USING (auth.uid()::text = author_id);
`;

    console.log(createTableSQL);
    console.log('=====================================');
    console.log('');
    console.log('After running the SQL, the discussions table will be ready for use.');

    console.log('✅ Instructions provided for manual table creation');

    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
applyDiscussionsMigrationDirect()
  .then(() => {
    console.log('✨ All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });

export { applyDiscussionsMigration, applyDiscussionsMigrationDirect };
