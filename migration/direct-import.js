#!/usr/bin/env node

/**
 * Direct import script for IsotopeAI data to Supabase
 * Use this if the web migration page has issues
 */

import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration - UPDATE THESE VALUES
const SUPABASE_URL = 'https://pcfrgvhigvklersufktf.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjZnJndmhpZ3ZrbGVyc3Vma3RmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc2MDkzNywiZXhwIjoyMDY0MzM2OTM3fQ.fKrM48gLm71Q7F6ioePKmX6xacrC6o-nasdmNJ6CULc'; // Get from Supabase dashboard

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

console.log('🚀 IsotopeAI Direct Import to Supabase');
console.log('=====================================\n');

// Parse CSV function
function parseCSV(csvText) {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
        const values = [];
        let current = '';
        let inQuotes = false;

        for (let j = 0; j < lines[i].length; j++) {
            const char = lines[i][j];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        values.push(current.trim());

        if (values.length === headers.length) {
            const row = {};
            headers.forEach((header, index) => {
                let value = values[index]?.replace(/^"|"$/g, '').replace(/""/g, '"') || '';
                
                // Parse JSON fields with special handling for malformed JSON
                if (header.includes('messages') || header.includes('members') ||
                    header.includes('stats') || header.includes('progress') ||
                    header.includes('subject_marks') || header.includes('studySessions') ||
                    header.includes('mockTests')) {
                    try {
                        if (value && value.trim() !== '') {
                            // Try to parse as-is first
                            value = JSON.parse(value);
                        } else {
                            value = {};
                        }
                    } catch (e) {
                        try {
                            // For malformed JSON from Firebase export, try using eval in a safe way
                            // This handles the unquoted keys issue
                            if (value.trim().startsWith('{') && value.trim().endsWith('}')) {
                                // Use Function constructor for safer eval
                                value = new Function('return ' + value)();
                            } else {
                                value = {};
                            }
                        } catch (e2) {
                            console.warn(`Failed to parse JSON for ${header}:`, value.substring(0, 100) + '...');
                            value = {};
                        }
                    }
                }

                // Parse boolean fields
                if (header.includes('is_') || header === 'completed' ||
                    header === 'welcomeEmailSent') {
                    value = value === 'true' || value === '1' || value === true;
                }

                // Parse numeric fields
                if (header.includes('count') || header.includes('marks') ||
                    header === 'duration') {
                    value = value ? parseInt(value) || 0 : 0;
                }

                // Handle empty strings - convert to null for optional fields
                if (value === '' && (header === 'displayName' || header === 'photoURL' ||
                    header === 'username' || header === 'backgroundImage' ||
                    header === 'bio' || header === 'location' || header === 'uid')) {
                    value = null;
                }

                row[header] = value;
            });
            data.push(row);
        }
    }

    return data;
}

// Import functions
async function importUsers(csvFile) {
    console.log('👥 Importing users...');

    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const users = parseCSV(csvText);

    console.log(`📊 Found ${users.length} users to import`);

    let imported = 0;
    let errors = 0;

    for (const user of users) {
        try {
            // Map CSV fields to correct Supabase column names
            const userData = {
                id: user.id || '',
                email: user.email || '',
                username: user.username || null,
                uid: user.uid || user.id || null, // Use uid from CSV or fallback to id
                displayName: user.displayName || null, // Correct field name
                photoURL: user.photoURL || null, // Correct field name
                created_at: user.created_at ? new Date(user.created_at).toISOString() : new Date().toISOString(),
                updated_at: user.updated_at ? new Date(user.updated_at).toISOString() : new Date().toISOString(),
                lastLogin: user.lastLogin ? new Date(user.lastLogin).toISOString() : null,
                welcomeEmailSent: user.welcomeEmailSent === true || user.welcomeEmailSent === 'true' || false,
                backgroundImage: user.backgroundImage || null,
                bio: user.bio || null,
                location: user.location || null,
                stats: user.stats && typeof user.stats === 'object' ? user.stats : {},
                progress: user.progress && typeof user.progress === 'object' ? user.progress : {},
                studySessions: user.studySessions && typeof user.studySessions === 'object' ? user.studySessions : {},
                mockTests: user.mockTests && typeof user.mockTests === 'object' ? user.mockTests : {}
            };

            // Log the first few users to debug
            if (imported < 3) {
                console.log(`🔍 User ${imported + 1} data:`, {
                    id: userData.id,
                    email: userData.email,
                    displayName: userData.displayName,
                    photoURL: userData.photoURL,
                    username: userData.username,
                    backgroundImage: userData.backgroundImage,
                    bio: userData.bio,
                    location: userData.location,
                    stats: Object.keys(userData.stats || {}).length > 0 ? 'Has data' : 'Empty',
                    progress: Object.keys(userData.progress || {}).length > 0 ? 'Has data' : 'Empty',
                    studySessions: Object.keys(userData.studySessions || {}).length > 0 ? 'Has data' : 'Empty',
                    mockTests: Object.keys(userData.mockTests || {}).length > 0 ? 'Has data' : 'Empty'
                });
            }

            const { error } = await supabase
                .from('users')
                .upsert(userData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing user ${user.id}:`, error.message);
                console.error(`❌ User data that failed:`, userData);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${users.length} users`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing user ${user.id}:`, err.message);
            console.error(`❌ User object:`, user);
            errors++;
        }
    }

    console.log(`✅ Users import complete: ${imported} imported, ${errors} errors\n`);
}

async function importGroups(csvFile) {
    console.log('👥 Importing groups...');

    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const groups = parseCSV(csvText);

    console.log(`📊 Found ${groups.length} groups to import`);

    let importedGroups = 0;
    let errors = 0;

    for (const group of groups) {
        try {
            // Map CSV fields to correct Supabase column names
            const groupData = {
                id: group.id || '',
                name: group.name || '',
                description: group.description || null,
                members: Array.isArray(group.members) ? group.members : [],
                createdBy: group.created_by || group.createdBy || '',
                createdAt: group.created_at || group.createdAt ?
                    new Date(group.created_at || group.createdAt).toISOString() :
                    new Date().toISOString(),
                isPublic: group.is_public === true || group.is_public === 'true' ||
                         group.isPublic === true || group.isPublic === 'true' || false,
                inviteCode: group.invite_code || group.inviteCode || null
            };

            // Log the first few groups to debug
            if (importedGroups < 3) {
                console.log(`🔍 Group ${importedGroups + 1} data:`, {
                    id: groupData.id,
                    name: groupData.name,
                    createdBy: groupData.createdBy,
                    members: groupData.members?.length || 0
                });
            }

            const { error: groupError } = await supabase
                .from('groups')
                .upsert(groupData, { onConflict: 'id' });

            if (groupError) {
                console.error(`❌ Error importing group ${group.id}:`, groupError.message);
                console.error(`❌ Group data that failed:`, groupData);
                errors++;
                continue;
            }

            importedGroups++;

            if (importedGroups % 5 === 0) {
                console.log(`✅ Imported ${importedGroups}/${groups.length} groups`);
            }
        } catch (err) {
            console.error(`❌ Exception importing group ${group.id}:`, err.message);
            console.error(`❌ Group object:`, group);
            errors++;
        }
    }

    console.log(`✅ Groups import complete: ${importedGroups} groups imported, ${errors} errors\n`);
}

async function importAIChats(csvFile) {
    console.log('🤖 Importing AI chats...');
    
    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const chats = parseCSV(csvText);
    
    console.log(`📊 Found ${chats.length} AI chats to import`);
    
    let imported = 0;
    let errors = 0;
    
    for (const chat of chats) {
        try {
            const chatData = {
                id: chat.id,
                user_id: chat.user_id,
                title: chat.title || null,
                messages: chat.messages || [],
                created_at: chat.created_at || new Date().toISOString(),
                updated_at: chat.updated_at || new Date().toISOString(),
                is_public: chat.is_public || false,
                view_count: chat.view_count || 0,
                created_by: chat.user_id,
                slug: chat.slug || null,
                preview: chat.preview || null,
                status: chat.status || 'approved',
                tags: chat.tags || [],
                is_pinned: chat.is_pinned || false,
                is_starred: chat.is_starred || false
            };

            const { error } = await supabase
                .from('aiChats')
                .upsert(chatData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing AI chat ${chat.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${chats.length} AI chats`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing AI chat ${chat.id}:`, err.message);
            errors++;
        }
    }
    
    console.log(`✅ AI chats import complete: ${imported} imported, ${errors} errors\n`);
}

async function importExams(csvFile) {
    console.log('📝 Importing exams...');
    
    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const exams = parseCSV(csvText);
    
    console.log(`📊 Found ${exams.length} exams to import`);
    
    let imported = 0;
    let errors = 0;
    
    for (const exam of exams) {
        try {
            const examData = {
                id: exam.id,
                user_id: exam.user_id,
                name: exam.name,
                date: exam.date, // Assuming date is already in a valid format (e.g., YYYY-MM-DD)
                subject_marks: exam.subject_marks || [],
                total_marks_obtained: exam.total_marks_obtained || 0,
                total_marks: exam.total_marks || 0,
                notes: exam.notes || null,
                created_at: exam.created_at || new Date().toISOString(),
                updated_at: exam.updated_at || new Date().toISOString()
            };

            const { error } = await supabase
                .from('exams')
                .upsert(examData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing exam ${exam.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${exams.length} exams`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing exam ${exam.id}:`, err.message);
            errors++;
        }
    }
    
    console.log(`✅ Exams import complete: ${imported} imported, ${errors} errors\n`);
}

async function importChats(csvFile) {
    console.log('💬 Importing chats...');

    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const chats = parseCSV(csvText);

    console.log(`📊 Found ${chats.length} chats to import`);

    let imported = 0;
    let errors = 0;

    for (const chat of chats) {
        try {
            const chatData = {
                id: chat.id || '',
                name: chat.name || '',
                description: chat.description || null,
                members: Array.isArray(chat.members) ? chat.members : [],
                createdBy: chat.createdBy || chat.created_by || '',
                createdAt: chat.createdAt || chat.created_at ?
                    new Date(chat.createdAt || chat.created_at).toISOString() :
                    new Date().toISOString(),
                updatedAt: chat.updatedAt || chat.updated_at ?
                    new Date(chat.updatedAt || chat.updated_at).toISOString() :
                    new Date().toISOString(),
                isPublic: chat.isPublic === true || chat.isPublic === 'true' ||
                         chat.is_public === true || chat.is_public === 'true' || false
            };

            const { error } = await supabase
                .from('chats')
                .upsert(chatData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing chat ${chat.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${chats.length} chats`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing chat ${chat.id}:`, err.message);
            errors++;
        }
    }

    console.log(`✅ Chats import complete: ${imported} imported, ${errors} errors\n`);
}

async function importTodos(csvFile) {
    console.log('📝 Importing todos...');

    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const todos = parseCSV(csvText);

    console.log(`📊 Found ${todos.length} todos to import`);

    let imported = 0;
    let errors = 0;

    for (const todo of todos) {
        try {
            const todoData = {
                id: todo.id || '',
                userId: todo.userId || todo.user_id || '',
                title: todo.title || '',
                description: todo.description || null,
                completed: todo.completed === true || todo.completed === 'true' || false,
                createdAt: todo.createdAt || todo.created_at ?
                    new Date(todo.createdAt || todo.created_at).toISOString() :
                    new Date().toISOString(),
                updatedAt: todo.updatedAt || todo.updated_at ?
                    new Date(todo.updatedAt || todo.updated_at).toISOString() :
                    new Date().toISOString(),
                dueDate: todo.dueDate || todo.due_date ?
                    new Date(todo.dueDate || todo.due_date).toISOString() : null,
                priority: todo.priority || 'medium',
                tags: Array.isArray(todo.tags) ? todo.tags : []
            };

            const { error } = await supabase
                .from('todos')
                .upsert(todoData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing todo ${todo.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${todos.length} todos`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing todo ${todo.id}:`, err.message);
            errors++;
        }
    }

    console.log(`✅ Todos import complete: ${imported} imported, ${errors} errors\n`);
}

async function importUserSubjects(csvFile) {
    console.log('📚 Importing user subjects...');

    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const userSubjects = parseCSV(csvText);

    console.log(`📊 Found ${userSubjects.length} user subjects to import`);

    let imported = 0;
    let errors = 0;

    for (const userSubject of userSubjects) {
        try {
            const userSubjectData = {
                id: userSubject.id || '',
                userId: userSubject.userId || userSubject.user_id || '',
                subjectName: userSubject.subjectName || userSubject.subject_name || '',
                color: userSubject.color || '#3b82f6',
                createdAt: userSubject.createdAt || userSubject.created_at ?
                    new Date(userSubject.createdAt || userSubject.created_at).toISOString() :
                    new Date().toISOString(),
                updatedAt: userSubject.updatedAt || userSubject.updated_at ?
                    new Date(userSubject.updatedAt || userSubject.updated_at).toISOString() :
                    new Date().toISOString(),
                isActive: userSubject.isActive === true || userSubject.isActive === 'true' ||
                         userSubject.is_active === true || userSubject.is_active === 'true' || true
            };

            const { error } = await supabase
                .from('userSubjects')
                .upsert(userSubjectData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing user subject ${userSubject.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${userSubjects.length} user subjects`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing user subject ${userSubject.id}:`, err.message);
            errors++;
        }
    }

    console.log(`✅ User subjects import complete: ${imported} imported, ${errors} errors\n`);
}

// Main import function
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node direct-import.js <csv-file> [csv-file2] [csv-file3]...');
        console.log('');
        console.log('Examples:');
        console.log('  node direct-import.js isotope-all-users.csv');
        console.log('  node direct-import.js isotope-all-groups.csv');
        console.log('  node direct-import.js isotope-all-ai-chats.csv');
        console.log('  node direct-import.js isotope-all-chats.csv');
        console.log('  node direct-import.js isotope-exams.csv');
        console.log('  node direct-import.js isotope-todos.csv');
        console.log('  node direct-import.js isotope-userSubjects.csv');
        console.log('  node direct-import.js *.csv  # Import all CSV files');
        console.log('');
        console.log('Supported file types: users, groups, ai-chats, chats, exams, todos, userSubjects');
        console.log('Make sure to update SUPABASE_SERVICE_KEY in this script first!');
        return;
    }

    if (SUPABASE_SERVICE_KEY === 'YOUR_SUPABASE_SERVICE_KEY') {
        console.log('❌ Please update SUPABASE_SERVICE_KEY in this script first!');
        console.log('   Get it from: Supabase Dashboard → Settings → API → service_role key');
        return;
    }

    for (const csvFile of args) {
        console.log(`📁 Processing: ${csvFile}`);

        if (csvFile.includes('users')) {
            await importUsers(csvFile);
        } else if (csvFile.includes('groups')) {
            await importGroups(csvFile);
        } else if (csvFile.includes('ai-chats') || csvFile.includes('aiChats')) {
            await importAIChats(csvFile);
        } else if (csvFile.includes('chats') && !csvFile.includes('ai-chats') && !csvFile.includes('aiChats')) {
            await importChats(csvFile);
        } else if (csvFile.includes('exams')) {
            await importExams(csvFile);
        } else if (csvFile.includes('todos')) {
            await importTodos(csvFile);
        } else if (csvFile.includes('userSubjects') || csvFile.includes('user-subjects')) {
            await importUserSubjects(csvFile);
        } else {
            console.log(`⚠️  Unknown file type: ${csvFile} - skipping`);
            console.log(`   Supported types: users, groups, ai-chats, chats, exams, todos, userSubjects`);
        }
    }
    
    console.log('🎉 Import complete!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Verify data in Supabase dashboard');
    console.log('2. Test the application with imported data');
    console.log('3. Begin code migration phase');
}

main().catch(console.error);
