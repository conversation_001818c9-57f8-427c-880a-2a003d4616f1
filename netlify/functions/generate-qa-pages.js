const fs = require('fs');
const path = require('path');
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs, orderBy } = require('firebase/firestore');

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Directory for generated pages
const OUTPUT_DIR = path.resolve(__dirname, '../../public/shared');

// Create the output directory if it doesn't exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Generate FAQPage schema JSON-LD for shared chats
function generateFAQPageSchema(chat) {
  // Prepare a FAQPage schema from the chat messages
  // Only include user messages as questions and AI responses as answers
  const qaItems = [];
  
  for (let i = 0; i < chat.messages.length; i++) {
    // Skip non-user messages (we only want questions from the user)
    if (!chat.messages[i].isUser) continue;
    
    // Get the question from this message
    const question = chat.messages[i].content;
    
    // Look for the next non-user message (the answer)
    let answer = '';
    for (let j = i + 1; j < chat.messages.length; j++) {
      if (!chat.messages[j].isUser) {
        answer = chat.messages[j].content;
        break;
      }
    }
    
    // If we found a question and answer pair, add to items
    if (question && answer) {
      qaItems.push({
        '@type': 'Question',
        'name': question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': answer
        }
      });
    }
  }
  
  // Only generate the schema if we have at least one Q&A pair
  if (qaItems.length === 0) return null;
  
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    'mainEntity': qaItems
  };
}

// Generate HTML for a shared chat page
function generateHtml(chat, baseUrl) {
  const pageUrl = `${baseUrl}/shared/${chat.slug || chat.id}`;
  const pageTitle = `${chat.title || 'Chat Conversation'} | IsotopeAI`;
  
  // Get the first message for the meta description (if available)
  let metaDescription = '';
  if (chat.messages && chat.messages.length > 0) {
    metaDescription = chat.messages[0].content.substring(0, 155) + '...';
  } else {
    metaDescription = 'Interactive AI chat conversation from IsotopeAI.';
  }
  
  // Generate FAQ schema for the conversation
  const schemaJson = generateFAQPageSchema(chat);
  
  let messagesHtml = '';
  
  // Generate HTML for each message in the chat
  if (chat.messages && chat.messages.length > 0) {
    messagesHtml = chat.messages.map(msg => {
      const messageClass = msg.isUser ? 'user-message' : 'ai-message';
      const messageStyle = msg.isUser 
        ? 'background-color: #f0f7ff; border-radius: 1rem; padding: 1rem; margin: 1rem 0; text-align: right;' 
        : 'background-color: #f8f9fa; border-radius: 1rem; padding: 1rem; margin: 1rem 0;';
      
      let imageHtml = '';
      if (msg.image && msg.image.url) {
        imageHtml = `<div class="message-image">
          <img src="${msg.image.url}" alt="Image attached to message" style="max-width: 100%; height: auto; border-radius: 0.5rem; margin-top: 0.5rem;">
        </div>`;
      }
      
      return `
        <div class="${messageClass}" style="${messageStyle}">
          <div class="message-header" style="font-weight: bold; margin-bottom: 0.5rem;">
            ${msg.isUser ? 'You' : 'AI Assistant'}
          </div>
          <div class="message-content" style="white-space: pre-line;">
            ${msg.content.replace(/\n/g, '<br>')}
          </div>
          ${imageHtml}
        </div>
      `;
    }).join('');
  } else {
    messagesHtml = '<p>No messages in this conversation.</p>';
  }
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageTitle}</title>
  <meta name="description" content="${metaDescription}">
  <link rel="canonical" href="${pageUrl}">
  <meta property="og:title" content="${pageTitle}">
  <meta property="og:description" content="${metaDescription}">
  <meta property="og:url" content="${pageUrl}">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="${pageTitle}">
  <meta name="twitter:description" content="${metaDescription}">
  <link rel="stylesheet" href="/assets/styles/main.css">
  ${schemaJson ? `<script type="application/ld+json">
    ${JSON.stringify(schemaJson)}
  </script>` : ''}
  <!-- Analytics, if any -->
</head>
<body>
  <header class="site-header" style="background-color: #0070f3; color: white; padding: 1rem; text-align: center;">
    <a href="/" class="logo" style="color: white; text-decoration: none; font-size: 1.5rem; font-weight: bold;">IsotopeAI</a>
    <nav style="margin-top: 1rem;">
      <a href="/" style="color: white; text-decoration: none; margin: 0 0.5rem;">Home</a>
      <a href="/ai" style="color: white; text-decoration: none; margin: 0 0.5rem;">AI Chat</a>
      <a href="/shared" style="color: white; text-decoration: none; margin: 0 0.5rem;">Shared Chats</a>
    </nav>
  </header>

  <main style="max-width: 800px; margin: 2rem auto; padding: 0 1rem;">
    <article class="chat-container">
      <header style="margin-bottom: 2rem;">
        <h1 style="font-size: 2rem; font-weight: bold; margin-bottom: 0.5rem;">${chat.title || 'Chat Conversation'}</h1>
        <div style="color: #666; font-size: 0.9rem;">
          Shared chat • ${chat.viewCount || 0} views
        </div>
      </header>
      
      <div class="chat-messages">
        ${messagesHtml}
      </div>
      
      <div class="interact-container" style="background-color: #f0f7ff; border-radius: 1rem; padding: 1.5rem; margin-top: 2rem; text-align: center;">
        <h3 style="font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem;">Continue this conversation</h3>
        <p style="margin-bottom: 1rem;">
          Want to ask follow-up questions or create your own AI conversation?
        </p>
        <a href="/ai" style="display: inline-block; background-color: #0070f3; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; font-weight: bold;">
          Try the AI Chat
        </a>
      </div>
    </article>
    
    <aside style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
      <h3 style="font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem;">Explore more conversations</h3>
      <div id="related-chats-placeholder" style="text-align: center;">
        <p>Loading related conversations...</p>
        <noscript>
          <p>JavaScript is required to load related conversations.</p>
        </noscript>
      </div>
    </aside>
  </main>

  <footer style="background-color: #f8f9fa; padding: 2rem 1rem; text-align: center; margin-top: 2rem;">
    <p>&copy; ${new Date().getFullYear()} IsotopeAI. All rights reserved.</p>
  </footer>

  <!-- Minimal JS to handle SPA hydration and related content -->
  <script>
    // Fetch related chats based on this chat
    async function loadRelatedChats() {
      try {
        const response = await fetch('/api/related-chats?chatId=${chat.id}');
        const data = await response.json();
        
        const container = document.getElementById('related-chats-placeholder');
        
        if (data.chats && data.chats.length > 0) {
          // Create list of related chats
          const list = document.createElement('div');
          list.style.display = 'grid';
          list.style.gridTemplateColumns = 'repeat(auto-fill, minmax(250px, 1fr))';
          list.style.gap = '1rem';
          
          data.chats.forEach(chat => {
            const item = document.createElement('div');
            item.style.border = '1px solid #eee';
            item.style.borderRadius = '0.5rem';
            item.style.padding = '1rem';
            
            const link = document.createElement('a');
            link.href = \`/shared/\${chat.slug || chat.id}\`;
            link.textContent = chat.title || 'Chat Conversation';
            link.style.textDecoration = 'none';
            link.style.color = '#0070f3';
            link.style.fontWeight = 'bold';
            link.style.display = 'block';
            link.style.marginBottom = '0.5rem';
            
            const preview = document.createElement('p');
            preview.textContent = chat.preview || '';
            preview.style.color = '#666';
            preview.style.fontSize = '0.9rem';
            preview.style.margin = '0';
            
            item.appendChild(link);
            item.appendChild(preview);
            list.appendChild(item);
          });
          
          // Replace placeholder with actual content
          container.innerHTML = '';
          container.appendChild(list);
        } else {
          container.innerHTML = '<p>No related conversations found.</p>';
        }
      } catch (error) {
        console.error('Error loading related chats:', error);
        document.getElementById('related-chats-placeholder').innerHTML = 
          '<p>Unable to load related conversations.</p>';
      }
    }
    
    // Load related chats when page loads
    window.addEventListener('DOMContentLoaded', loadRelatedChats);
  </script>
</body>
</html>`;
}

// Main function to generate all shared chat pages
async function generateSharedChatPages() {
  console.log('Starting shared chat page generation...');
  
  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);
  
  // Get base URL from environment
  const baseUrl = process.env.URL || 'https://isotopeai.in';
  
  try {
    // Query for all public shared chats
    const chatsRef = collection(db, 'aiChats');
    const q = query(
      chatsRef,
      where('isPublic', '==', true),
      orderBy('updatedAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    console.log(`Found ${querySnapshot.size} public shared chats to generate...`);
    
    // Create sitemap entries while generating pages
    const sitemapEntries = [];
    const today = new Date().toISOString().split('T')[0];
    
    // Generate a static HTML page for each shared chat
    for (const doc of querySnapshot.docs) {
      const chat = {
        id: doc.id,
        ...doc.data()
      };
      
      // Generate the slug if it doesn't exist
      const slug = chat.slug || chat.id;
      
      // Generate HTML content
      const htmlContent = generateHtml(chat, baseUrl);
      
      // Write to file
      const filePath = path.join(OUTPUT_DIR, `${slug}.html`);
      fs.writeFileSync(filePath, htmlContent);
      
      console.log(`Generated page for: ${slug}`);
      
      // Add to sitemap entries
      sitemapEntries.push(`
  <url>
    <loc>${baseUrl}/shared/${slug}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`);
    }
    
    // Generate shared chats sitemap
    const sharedChatsSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Shared Chat Pages -->${sitemapEntries.join('')}
</urlset>`;
    
    // Write sitemap to file
    fs.writeFileSync(path.join(OUTPUT_DIR, '../sitemap-shared.xml'), sharedChatsSitemap);
    
    console.log(`Shared chat pages generation complete! Generated ${sitemapEntries.length} pages and sitemap.`);
    return {
      success: true,
      pagesGenerated: sitemapEntries.length
    };
  } catch (error) {
    console.error('Error generating shared chat pages:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Export for use as Netlify function
exports.handler = async function(event, context) {
  try {
    const result = await generateSharedChatPages();
    
    return {
      statusCode: 200,
      body: JSON.stringify(result)
    };
  } catch (error) {
    console.error('Error in function handler:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ success: false, error: error.message })
    };
  }
};

// Export for use in build scripts
exports.generateSharedChatPages = generateSharedChatPages;