const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs, orderBy, limit, doc, getDoc } = require('firebase/firestore');

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Firebase initialization
let firebaseApp;
let db;

// Helper function to extract keywords from text
function extractKeywords(text) {
  if (!text) return [];
  
  // Remove special characters and convert to lowercase
  const cleanText = text.toLowerCase().replace(/[^\w\s]/g, '');
  
  // Split into words
  const words = cleanText.split(/\s+/);
  
  // Filter out common stop words
  const stopWords = new Set([
    'a', 'about', 'above', 'after', 'again', 'against', 'all', 'am', 'an', 'and', 'any', 'are', 'as', 'at',
    'be', 'because', 'been', 'before', 'being', 'below', 'between', 'both', 'but', 'by',
    'can', 'did', 'do', 'does', 'doing', 'down', 'during',
    'each', 'few', 'for', 'from', 'further',
    'had', 'has', 'have', 'having', 'he', 'her', 'here', 'hers', 'herself', 'him', 'himself', 'his', 'how',
    'i', 'if', 'in', 'into', 'is', 'it', 'its', 'itself',
    'just',
    'me', 'more', 'most', 'my', 'myself',
    'no', 'nor', 'not', 'now',
    'of', 'off', 'on', 'once', 'only', 'or', 'other', 'our', 'ours', 'ourselves', 'out', 'over', 'own',
    'same', 'she', 'should', 'so', 'some', 'such',
    'than', 'that', 'the', 'their', 'theirs', 'them', 'themselves', 'then', 'there', 'these', 'they',
    'this', 'those', 'through', 'to', 'too',
    'under', 'until', 'up',
    'very',
    'was', 'we', 'were', 'what', 'when', 'where', 'which', 'while', 'who', 'whom', 'why', 'will', 'with',
    'would',
    'you', 'your', 'yours', 'yourself', 'yourselves'
  ]);
  
  const keywords = words.filter(word => 
    word.length > 2 && !stopWords.has(word)
  );
  
  // Return unique keywords
  return [...new Set(keywords)];
}

// Find related chats based on a given chat ID
async function findRelatedChats(chatId, maxCount = 6) {
  if (!firebaseApp) {
    firebaseApp = initializeApp(firebaseConfig);
    db = getFirestore(firebaseApp);
  }
  
  try {
    // First, get the current chat data
    const chatRef = collection(db, 'aiChats');
    const chatDoc = await getDoc(doc(db, 'aiChats', chatId));
    
    if (!chatDoc.exists()) {
      return [];
    }
    
    const currentChat = chatDoc.data();
    
    // Extract all messages content
    const allMessages = currentChat.messages || [];
    const allText = allMessages.map(msg => msg.content).join(' ');
    
    // Extract keywords from all messages
    const currentKeywords = extractKeywords(allText);
    
    if (currentKeywords.length === 0) {
      // If no keywords found, just return recent chats
      const recentQuery = query(
        chatRef,
        where('isPublic', '==', true),
        where('id', '!=', chatId),
        orderBy('updatedAt', 'desc'),
        limit(maxCount)
      );
      
      const recentSnapshot = await getDocs(recentQuery);
      return recentSnapshot.docs.map(doc => {
        const data = doc.data();
        const firstMessage = data.messages && data.messages.length > 0 ? data.messages[0].content : '';
        return {
          id: doc.id,
          slug: data.slug || doc.id,
          title: data.title || firstMessage.substring(0, 50) + '...',
          preview: firstMessage.substring(0, 120) + '...'
        };
      });
    }
    
    // Get all public chats
    const allChatsQuery = query(
      chatRef,
      where('isPublic', '==', true)
    );
    
    const allChatsSnapshot = await getDocs(allChatsQuery);
    
    // Calculate relevance scores
    const scoredChats = allChatsSnapshot.docs
      .filter(doc => doc.id !== chatId) // Exclude current chat
      .map(doc => {
        const data = doc.data();
        const messages = data.messages || [];
        const allText = messages.map(msg => msg.content).join(' ');
        const chatKeywords = extractKeywords(allText);
        
        // Calculate matches with current keywords
        let matchCount = 0;
        
        for (const keyword of currentKeywords) {
          if (chatKeywords.includes(keyword)) {
            matchCount++;
          }
        }
        
        // Calculate relevance score (percentage of matching keywords)
        const score = matchCount / Math.max(1, currentKeywords.length);
        
        // Get the first message for preview
        const firstMessage = messages.length > 0 ? messages[0].content : '';
        
        return {
          id: doc.id,
          slug: data.slug || doc.id,
          title: data.title || firstMessage.substring(0, 50) + '...',
          preview: firstMessage.substring(0, 120) + '...',
          score: score
        };
      })
      .filter(item => item.score > 0) // Only keep items with some relevance
      .sort((a, b) => b.score - a.score) // Sort by relevance, highest first
      .slice(0, maxCount);
    
    // If we don't have enough relevant chats, supplement with recent ones
    if (scoredChats.length < maxCount) {
      const recentQuery = query(
        chatRef,
        where('isPublic', '==', true),
        where('id', '!=', chatId),
        orderBy('updatedAt', 'desc'),
        limit(maxCount)
      );
      
      const recentSnapshot = await getDocs(recentQuery);
      const recentChats = recentSnapshot.docs
        .map(doc => {
          const data = doc.data();
          const firstMessage = data.messages && data.messages.length > 0 ? data.messages[0].content : '';
          return {
            id: doc.id,
            slug: data.slug || doc.id,
            title: data.title || firstMessage.substring(0, 50) + '...',
            preview: firstMessage.substring(0, 120) + '...',
            score: 0
          };
        })
        .filter(item => !scoredChats.some(c => c.id === item.id));
      
      // Combine the lists, keeping the max count
      const combined = [...scoredChats, ...recentChats].slice(0, maxCount);
      return combined;
    }
    
    return scoredChats;
  } catch (error) {
    console.error('Error finding related chats:', error);
    return [];
  }
}

// Netlify function handler
exports.handler = async function(event, context) {
  // Extract chat ID from query parameters
  const chatId = event.queryStringParameters?.chatId;
  
  if (!chatId) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Missing required parameter: chatId' })
    };
  }
  
  try {
    const relatedChats = await findRelatedChats(chatId);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      },
      body: JSON.stringify({
        chats: relatedChats
      })
    };
  } catch (error) {
    console.error('Error handling related chats request:', error);
    
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}; 