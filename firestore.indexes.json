{"indexes": [{"collectionGroup": "aiChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "aiChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "viewCount", "order": "DESCENDING"}]}, {"collectionGroup": "aiChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "aiChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "aiChats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "groups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}