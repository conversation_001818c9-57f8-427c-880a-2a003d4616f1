const CACHE_VERSION = 'v4';
const CACHE_NAME = `isotopeai-${CACHE_VERSION}`;
const DYNAMIC_CACHE = `dynamic-${CACHE_VERSION}`;
const API_CACHE = `api-${CACHE_VERSION}`;

const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/icon-192x192.png',
  '/icon-512x512.png',
  '/google.svg',
  '/assets/*',
  'https://fonts.googleapis.com/css2?family=Anta&display=swap'
];

const PRECACHE_URLS = [
  '/',
  '/index.html',
  '/ai',
  '/groups',
  '/productivity',
  '/tasks',
  '/analytics',
  '/login',
  '/profile-setup',
  '/ai-landing',
  '/groups-landing',
  '/productivity-landing',
  '/tasks-landing',
  '/about-us',
  '/contact-us',
  '/fullscreen',
  '/manifest.json',
  '/favicon.ico',
  '/icon-192x192.png',
  '/icon-512x512.png'
];

// Network-first routes (always try network first, fall back to cache)
const NETWORK_FIRST_ROUTES = [
  '/ai',
  '/login',
  '/fullscreen',
  // AI endpoints should always be network-first to ensure fresh responses
  '/api/ai/',
  '/api/gemini/'
];

// Cache-first routes (use cache first, fall back to network)
const CACHE_FIRST_ROUTES = [
  '/manifest.json',
  '/favicon.ico',
  '/icon-192x192.png',
  '/icon-512x512.png',
  '/google.svg',
  '/assets/',
  'https://fonts.googleapis.com'
];

// Function to clear non-essential caches
const clearNonEssentialCaches = async () => {
  const cacheNames = await caches.keys();
  const currentCaches = [CACHE_NAME, DYNAMIC_CACHE, API_CACHE];

  return Promise.all(
    cacheNames.map(cacheName => {
      // Only delete caches that are not in our current version list
      // and are not related to Firebase, auth, chat, or firestore
      if (!currentCaches.includes(cacheName) &&
          !cacheName.includes('firebase') &&
          !cacheName.includes('auth') &&
          !cacheName.includes('chat') &&
          !cacheName.includes('firestore')) {
        return caches.delete(cacheName);
      }
      return Promise.resolve();
    })
  );
};

// Install event handler
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(CACHE_NAME).then((cache) => {
        console.log('Pre-caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      caches.open(DYNAMIC_CACHE).then((cache) => {
        console.log('Pre-caching dynamic routes');
        return cache.addAll(PRECACHE_URLS);
      }),
      clearNonEssentialCaches()
    ])
  );
  self.skipWaiting();
});

// Activate event handler - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    Promise.all([
      clearNonEssentialCaches(),
      self.clients.claim()
    ])
  );
});

// Helper function to determine caching strategy based on URL
const getCachingStrategy = (url) => {
  const pathname = new URL(url).pathname;

  // Check Network-first routes
  if (NETWORK_FIRST_ROUTES.some(route => pathname.startsWith(route))) {
    return 'network-first';
  }

  // Check Cache-first routes
  if (CACHE_FIRST_ROUTES.some(route => url.includes(route))) {
    return 'cache-first';
  }

  // Default to stale-while-revalidate
  return 'stale-while-revalidate';
};

// Create offline fallback page response
const createOfflineFallbackResponse = () => {
  return caches.match('/index.html')
    .then(cachedResponse => {
      if (cachedResponse) {
        return cachedResponse;
      }
      // If we don't have the index in cache, return a basic offline message
      return new Response(
        '<html><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
        {
          headers: { 'Content-Type': 'text/html' }
        }
      );
    });
};

// Fetch event handler with adaptive caching strategies
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests except for fonts
  if (!event.request.url.startsWith(self.location.origin) &&
      !event.request.url.includes('fonts.googleapis.com')) {
    return;
  }

  // Handle only GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  const url = event.request.url;
  const strategy = getCachingStrategy(url);

  // Apply different strategies based on URL
  if (strategy === 'network-first') {
    // Network-first strategy
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Only cache successful responses
          if (response.ok) {
            const responseClone = response.clone();
            caches.open(DYNAMIC_CACHE).then(cache => {
              cache.put(event.request, responseClone);
            });
          }
          return response;
        })
        .catch(() => {
          // If network fails, try from cache
          return caches.match(event.request)
            .then(cachedResponse => {
              return cachedResponse || createOfflineFallbackResponse();
            });
        })
    );
  } else if (strategy === 'cache-first') {
    // Cache-first strategy
    event.respondWith(
      caches.match(event.request)
        .then(cachedResponse => {
          // Return cached version or fetch from network
          return cachedResponse || fetch(event.request)
            .then(networkResponse => {
              // Only cache successful responses
              if (networkResponse.ok) {
                const responseClone = networkResponse.clone();
                caches.open(CACHE_NAME).then(cache => {
                  cache.put(event.request, responseClone);
                });
              }
              return networkResponse;
            })
            .catch(() => {
              // If it's an HTML request and we can't get it, show offline page
              if (event.request.headers.get('accept')?.includes('text/html')) {
                return createOfflineFallbackResponse();
              }
              // Otherwise just fail
              return new Response('Not available while offline', {
                status: 503,
                statusText: 'Service Unavailable'
              });
            });
        })
    );
  } else {
    // Stale-while-revalidate strategy (default)
    event.respondWith(
      caches.match(event.request).then(cachedResponse => {
        const fetchPromise = fetch(event.request)
          .then(networkResponse => {
            // Only cache successful responses
            if (networkResponse.ok) {
              const responseClone = networkResponse.clone();
              caches.open(DYNAMIC_CACHE).then(cache => {
                cache.put(event.request, responseClone);
              });
            }
            return networkResponse;
          })
          .catch(() => {
            // Network failed, if it's an HTML request, show offline page
            if (event.request.headers.get('accept')?.includes('text/html')) {
              return createOfflineFallbackResponse();
            }
            // For other resources, we have to fail
            throw new Error('Network unavailable');
          });

        // Return cached response immediately or wait for network
        return cachedResponse || fetchPromise;
      })
    );
  }
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-offline-actions') {
    event.waitUntil(
      // Sync logic would go here - implement if needed
      Promise.resolve()
    );
  }
});


