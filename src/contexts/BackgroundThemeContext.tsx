import { createContext, useContext, ReactNode } from 'react';

// Define only the default mountain theme
const defaultTheme = {
  type: 'image',
  value: 'https://wallpapercave.com/wp/wp9098846.jpg',
  label: 'Mountain Dark'
};

// Simplified context type
interface BackgroundThemeContextType {
  getBackgroundStyle: () => string;
}

const BackgroundThemeContext = createContext<BackgroundThemeContextType | undefined>(undefined);

export function BackgroundThemeProvider({ children }: { children: ReactNode }) {

  // Always return the style for the default mountain theme
  const getBackgroundStyle = () => {
    return `bg-[url('${defaultTheme.value}')] bg-cover bg-center`;
  };

  // Provide only the getBackgroundStyle function
  return (
    <BackgroundThemeContext.Provider value={{ getBackgroundStyle }}>
      {children}
    </BackgroundThemeContext.Provider>
  );
}

export function useBackgroundTheme() {
  const context = useContext(BackgroundThemeContext);
  if (context === undefined) {
    throw new Error('useBackgroundTheme must be used within a BackgroundThemeProvider');
  }
  return context;
}
