import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ArrowRight, HelpCircle } from 'lucide-react';

interface TutorialStep {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface PageTutorialProps {
  tutorialKey: string;
  tutorialSteps: TutorialStep[];
  showHelpButton?: boolean;
}

export function PageTutorial({ tutorialKey, tutorialSteps, showHelpButton = true }: PageTutorialProps) {
  const [showTutorial, setShowTutorial] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  
  useEffect(() => {
    // Check if this is the first visit to the page
    const hasSeenTutorial = localStorage.getItem(`hasSeen${tutorialKey}Tutorial`);
    if (!hasSeenTutorial) {
      setShowTutorial(true);
    }
  }, [tutorialKey]);
  
  const handleNextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeTutorial();
    }
  };
  
  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const completeTutorial = () => {
    // Mark tutorial as seen
    localStorage.setItem(`hasSeen${tutorialKey}Tutorial`, 'true');
    setShowTutorial(false);
  };
  
  const skipTutorial = () => {
    // Mark tutorial as seen but allow user to skip
    localStorage.setItem(`hasSeen${tutorialKey}Tutorial`, 'true');
    setShowTutorial(false);
  };
  
  const showTutorialManually = () => {
    setCurrentStep(0);
    setShowTutorial(true);
  };
  
  return (
    <>
      {showHelpButton && (
        <div className="fixed bottom-6 left-6 z-50">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-purple-600 hover:bg-purple-700 text-white border-none shadow-lg"
            onClick={showTutorialManually}
            title={`Show ${tutorialKey} Tutorial`}
          >
            <HelpCircle className="h-5 w-5" />
          </Button>
        </div>
      )}
      
      <Dialog open={showTutorial} onOpenChange={setShowTutorial}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <div className="flex justify-center mb-4">
              {tutorialSteps[currentStep].icon}
            </div>
            <DialogTitle className="text-xl text-center">
              {tutorialSteps[currentStep].title}
            </DialogTitle>
            <DialogDescription className="text-center pt-2">
              {tutorialSteps[currentStep].description}
            </DialogDescription>
          </DialogHeader>
          
          {/* Step indicator */}
          <div className="flex justify-center gap-1 py-4">
            {tutorialSteps.map((_, index) => (
              <div 
                key={index} 
                className={`h-2 w-2 rounded-full ${
                  index === currentStep ? 'bg-purple-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          
          <DialogFooter className="flex justify-between items-center">
            <div>
              {currentStep > 0 ? (
                <Button variant="outline" onClick={handlePrevStep}>
                  Back
                </Button>
              ) : (
                <Button variant="outline" onClick={skipTutorial}>
                  Skip
                </Button>
              )}
            </div>
            
            <Button onClick={handleNextStep}>
              {currentStep < tutorialSteps.length - 1 ? (
                <>
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              ) : (
                'Got it!'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 