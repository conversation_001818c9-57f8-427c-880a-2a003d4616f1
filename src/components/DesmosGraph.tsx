import React, { useEffect, useRef } from 'react';

declare global {
  interface Window {
    Desmos: any;
  }
}

interface DesmosGraphProps {
  expression: string;
  width?: string;
  height?: string;
}

export const DesmosGraph: React.FC<DesmosGraphProps> = ({ 
  expression, 
  width = '100%', 
  height = '400px' 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const calculatorRef = useRef<any>(null);

  useEffect(() => {
    // Load Desmos script if not already loaded
    if (!window.Desmos) {
      const script = document.createElement('script');
      script.src = 'https://www.desmos.com/api/v1.8/calculator.js?apiKey=dcb31709b452b1cf9dc26972add0fda6';
      script.async = true;
      script.onload = initializeCalculator;
      document.body.appendChild(script);
    } else {
      initializeCalculator();
    }

    return () => {
      if (calculatorRef.current) {
        calculatorRef.current.destroy();
        calculatorRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (calculatorRef.current) {
      try {
        calculatorRef.current.setExpression({ id: 'graph1', latex: expression });
      } catch (error) {
        console.error('Error setting Desmos expression:', error);
      }
    }
  }, [expression]);

  const initializeCalculator = () => {
    if (containerRef.current && !calculatorRef.current && window.Desmos) {
      calculatorRef.current = window.Desmos.GraphingCalculator(containerRef.current, {
        expressions: false,
        settingsMenu: false,
        zoomButtons: true,
        lockViewport: false,
        border: true
      });
      calculatorRef.current.setExpression({ id: 'graph1', latex: expression });
    }
  };

  return (
    <div 
      ref={containerRef} 
      style={{ 
        width, 
        height,
        margin: '1rem 0',
        border: '1px solid #e2e8f0',
        borderRadius: '0.5rem',
        overflow: 'hidden'
      }} 
    />
  );
}; 