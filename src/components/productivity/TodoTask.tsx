import { useState, useEffect } from 'react';
import { Draggable } from '@hello-pangea/dnd';
import { TodoItem } from '@/types/todo';
import { format, startOfDay } from 'date-fns';
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Clock, Trash2, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useSupabaseTodoStore } from '@/stores/supabaseTodoStore';

interface TodoTaskProps {
  task: TodoItem;
  index: number;
  isBeingDragged?: boolean;
}

export function TodoTask({ task, index, isBeingDragged = false }: TodoTaskProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(task.title);
  const [description, setDescription] = useState(task.description);
  const [priority, setPriority] = useState(task.priority);
  const [dueDate, setDueDate] = useState<Date | undefined>(
    task.dueDate ? new Date(task.dueDate) : undefined
  );
  const [descriptionError, setDescriptionError] = useState('');
  
  // Debug logging
  useEffect(() => {
    console.log(`Task ${task.id} rendered:`, task);
  }, [task]);
  
  const { updateTask, deleteTask } = useSupabaseTodoStore();
  
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500 hover:bg-red-600';
      case 'medium':
        return 'bg-yellow-500 hover:bg-yellow-600';
      case 'low':
        return 'bg-green-500 hover:bg-green-600';
      default:
        return 'bg-blue-500 hover:bg-blue-600';
    }
  };
  
  const handleSave = async () => {
    // Reset error state
    setDescriptionError('');
    
    // Validate inputs
    if (!title.trim()) return;
    
    if (!description.trim()) {
      setDescriptionError('Description is required');
      return;
    }
    
    await updateTask(task.id, {
      title,
      description,
      priority,
      dueDate: dueDate ? dueDate.getTime() : undefined,
    });
    setIsEditing(false);
  };
  
  const handleDelete = async () => {
    await deleteTask(task.id);
    setIsEditing(false);
  };
  
  const isOverdue = (): boolean => {
    if (!task.dueDate) return false;
    const today = startOfDay(new Date());
    const taskDueDate = startOfDay(new Date(task.dueDate));
    return taskDueDate < today;
  };
  
  return (
    <>
      <Draggable draggableId={task.id} index={index}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={`mb-2 transition-all ${isBeingDragged ? 'opacity-50' : ''}`}
            style={{
              ...provided.draggableProps.style,
            }}
          >
            {/* Theme-aware Card */}
            <Card 
              className={`shadow-sm hover:shadow-md transition-all bg-card border border-border ${
                isOverdue() ? 'border-red-500 dark:border-red-700 bg-red-50 dark:bg-red-950/20' : ''
              } ${snapshot.isDragging ? 'shadow-lg scale-[1.02] z-10' : ''}`}
              onClick={() => !snapshot.isDragging && setIsEditing(true)}
            >
              <CardHeader className="p-3 pb-0 cursor-pointer">
                <div className="flex justify-between items-start">
                  {/* Theme-aware CardTitle */}
                  <CardTitle className="text-sm font-medium text-card-foreground">
                    {task.title}
                  </CardTitle>
                  {/* Keep priority badge as is */}
                  <Badge className={`${getPriorityColor(task.priority)} text-white text-xs`}>
                    {task.priority}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="p-3 pt-2">
                {/* Theme-aware description text */}
                <p className="text-xs text-muted-foreground dark:text-gray-400 line-clamp-2">{task.description}</p>
              </CardContent>
              <CardFooter className="p-3 pt-0 flex justify-between items-center">
                <div className="flex items-center">
                  {task.dueDate && (
                    // Theme-aware due date text (keep overdue styling)
                    <div className={`flex items-center text-xs ${
                      isOverdue() 
                        ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-1.5 py-0.5 rounded-sm font-semibold' 
                        : 'text-muted-foreground dark:text-gray-400'
                    }`}>
                      <Clock className="h-3 w-3 mr-1" />
                      {format(new Date(task.dueDate), 'MMM d, yyyy')}
                    </div>
                  )}
                </div>
                {task.assignedTo && (
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={task.assignedToPhotoURL} alt={task.assignedToName} />
                    <AvatarFallback>{task.assignedToName?.charAt(0)}</AvatarFallback>
                  </Avatar>
                )}
              </CardFooter>
            </Card>
          </div>
        )}
      </Draggable>
      
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="title" className="text-sm font-medium">
                Title
              </label>
              {/* Theme-aware Input */}
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Task title"
                className="bg-muted border-border"
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              {/* Theme-aware Textarea */}
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe your task in detail. What needs to be done? Any specific requirements or notes?"
                rows={3}
                className={`${descriptionError ? "border-red-500" : "border-border"} bg-muted`}
              />
              {descriptionError && (
                <p className="text-sm text-red-500 mt-1">{descriptionError}</p>
              )}
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="priority" className="text-sm font-medium">
                Priority
              </label>
              {/* Theme-aware Select */}
              <Select
                value={priority}
                onValueChange={(value) => setPriority(value as 'low' | 'medium' | 'high')}
              >
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent className="bg-popover border-border text-popover-foreground">
                  <SelectItem value="low" className="hover:bg-accent">Low</SelectItem>
                  <SelectItem value="medium" className="hover:bg-accent">Medium</SelectItem>
                  <SelectItem value="high" className="hover:bg-accent">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="dueDate" className="text-sm font-medium">
                Due Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  {/* Theme-aware Button */}
                  <Button
                    variant="outline"
                    className="justify-start text-left font-normal bg-muted border-border hover:bg-accent"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                {/* Theme-aware Popover Content & Calendar */}
                <PopoverContent className="w-auto p-0 bg-popover border-border text-popover-foreground">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={setDueDate}
                    initialFocus
                    className="bg-popover text-popover-foreground" // Ensure calendar itself is themed
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <DialogFooter className="flex justify-between">
            {/* Destructive button (styles likely ok) */}
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
            {/* Primary button (styles likely ok) */}
            <Button onClick={handleSave}>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
