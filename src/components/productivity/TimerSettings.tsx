import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Settings } from "lucide-react"

interface TimerSettings {
  workDuration: number
  shortBreakDuration: number
  longBreakDuration: number
  cyclesBeforeLongBreak: number
}

interface TimerSettingsProps {
  settings: TimerSettings
  onUpdateSettings: (settings: TimerSettings) => void
}

export function TimerSettings({ settings, onUpdateSettings }: TimerSettingsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [localSettings, setLocalSettings] = useState(settings)

  const handleSave = () => {
    onUpdateSettings(localSettings)
    setIsOpen(false)
  }

  const handleChange = (field: keyof TimerSettings, value: string) => {
    const numValue = parseInt(value, 10)
    if (!isNaN(numValue) && numValue > 0) {
      setLocalSettings(prev => ({
        ...prev,
        [field]: field.includes('Duration') ? numValue * 60 : numValue
      }))
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Timer Settings</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="workDuration">Work Duration (minutes)</Label>
            <Input
              id="workDuration"
              type="number"
              min="1"
              value={Math.floor(localSettings.workDuration / 60)}
              onChange={(e) => handleChange('workDuration', e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="shortBreak">Short Break (minutes)</Label>
            <Input
              id="shortBreak"
              type="number"
              min="1"
              value={Math.floor(localSettings.shortBreakDuration / 60)}
              onChange={(e) => handleChange('shortBreakDuration', e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="longBreak">Long Break (minutes)</Label>
            <Input
              id="longBreak"
              type="number"
              min="1"
              value={Math.floor(localSettings.longBreakDuration / 60)}
              onChange={(e) => handleChange('longBreakDuration', e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="cycles">Cycles before Long Break</Label>
            <Input
              id="cycles"
              type="number"
              min="1"
              value={localSettings.cyclesBeforeLongBreak}
              onChange={(e) => handleChange('cyclesBeforeLongBreak', e.target.value)}
            />
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={handleSave}>Save Settings</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 