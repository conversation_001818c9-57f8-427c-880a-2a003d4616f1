import { useState, useRef, useLayoutEffect } from "react"
import { Check, ChevronsUpDown, Plus } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useLocalStorage } from "@/hooks/useLocalStorage"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"

const DEFAULT_SUBJECTS = [
  { id: "physics", name: "Physics" },
  { id: "chemistry", name: "Chemistry" },
  { id: "mathematics", name: "Mathematics" },
  { id: "biology", name: "Biology" },
  { id: "computer-science", name: "Computer Science" },
]

interface Subject {
  id: string
  name: string
}

interface SubjectSelectProps {
  selectedSubject: Subject | null
  onSubjectChange: (subject: Subject | null) => void
}

export function SubjectSelect({ selectedSubject, onSubjectChange }: SubjectSelectProps) {
  const [open, setOpen] = useState(false)
  const [isAddingSubject, setIsAddingSubject] = useState(false)
  const [newSubjectName, setNewSubjectName] = useState("")
  const [customSubjects, setCustomSubjects] = useLocalStorage<Subject[]>("customSubjects", [])
  const [buttonWidth, setButtonWidth] = useState("auto")
  const textRef = useRef<HTMLSpanElement>(null)
  
  const allSubjects = [...DEFAULT_SUBJECTS, ...customSubjects]

  // Update button width based on text content
  useLayoutEffect(() => {
    if (textRef.current) {
      const width = textRef.current.scrollWidth + 40 // Add padding for the chevron
      setButtonWidth(`${width}px`)
    }
  }, [selectedSubject])

  const handleAddSubject = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newSubjectName.trim()) {
      return
    }

    try {
      const newSubject = {
        id: newSubjectName.toLowerCase().replace(/\s+/g, '-'),
        name: newSubjectName.trim()
      }

      // Check for duplicate names
      if (allSubjects.some(subject => 
        subject.name.toLowerCase() === newSubject.name.toLowerCase()
      )) {
        toast({
          title: "Subject already exists",
          description: "Please use a different name.",
          variant: "destructive"
        })
        return
      }

      setCustomSubjects(prev => [...prev, newSubject])
      onSubjectChange(newSubject) // Automatically select the new subject
      setNewSubjectName("")
      setIsAddingSubject(false)
      
      toast({
        title: "Subject added",
        description: `${newSubject.name} has been added to your subjects.`
      })
    } catch (error) {
      console.error("Error adding subject:", error)
      toast({
        title: "Error",
        description: "Failed to add subject. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleOpenChange = (open: boolean) => {
    setIsAddingSubject(open)
    if (!open) {
      setNewSubjectName("")
    }
  }

  return (
    <div className="flex gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="relative px-6"
            style={{ width: buttonWidth }}
          >
            <span ref={textRef} className="truncate mx-auto text-center block">
              {selectedSubject ? selectedSubject.name : "Select subject..."}
            </span>
            <ChevronsUpDown className="absolute right-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0" style={{ width: buttonWidth, minWidth: '120px' }}>
          <Command className="w-full">
            <CommandInput placeholder="Search subjects..." className="text-center" />
            <CommandEmpty className="text-center py-2">No subject found.</CommandEmpty>
            <CommandGroup>
              {allSubjects.map((subject) => (
                <CommandItem
                  key={subject.id}
                  value={subject.name}
                  className="text-center justify-center flex-col items-center"
                  onSelect={() => {
                    onSubjectChange(subject)
                    setOpen(false)
                  }}
                >
                  <div className="flex items-center justify-center w-full">
                    <Check
                      className={cn(
                        "absolute left-2 h-4 w-4",
                        selectedSubject?.id === subject.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <span className="text-center">{subject.name}</span>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      <Button
        variant="outline"
        size="icon"
        onClick={() => setIsAddingSubject(true)}
      >
        <Plus className="h-4 w-4" />
      </Button>

      <Dialog open={isAddingSubject} onOpenChange={handleOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Subject</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAddSubject} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Input
                placeholder="Enter subject name"
                value={newSubjectName}
                onChange={(e) => setNewSubjectName(e.target.value)}
                autoFocus
              />
            </div>
            <Button type="submit" disabled={!newSubjectName.trim()}>
              Add Subject
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
} 