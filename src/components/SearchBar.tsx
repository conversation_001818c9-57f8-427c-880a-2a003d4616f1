import { Search, Image, X, Send } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { useState, useEffect } from "react";
import { ImageEditor } from "./ImageEditor";
import { Tools } from "./Tools";
import { cn } from "@/lib/utils";

interface SearchBarProps {
  onSubmit?: (query: string, image?: File) => void;
  setIsChatVisible?: (isVisible: boolean) => void;
  handleSendMessage?: (query: string, image?: File) => void;
  onToolOutput?: (output: any) => void;
  isChatVisible?: boolean;
  selectedImage?: File | null;
  setSelectedImage?: (image: File | null) => void;
  showImageEditor?: boolean;
  setShowImageEditor?: (show: boolean) => void;
  customPlaceholder?: string;
  customClassName?: string;
}

export const SearchBar = ({
  onSubmit,
  setIsChatVisible,
  handleSendMessage,
  onToolOutput,
  isChatVisible,
  selectedImage: externalSelectedImage,
  setSelectedImage: externalSetSelectedImage,
  showImageEditor: externalShowImageEditor,
  setShowImageEditor: externalSetShowImageEditor,
  customPlaceholder,
  customClassName
}: SearchBarProps) => {
  const [query, setQuery] = useState("");
  const [localSelectedImage, setLocalSelectedImage] = useState<File | null>(null);
  const [localShowImageEditor, setLocalShowImageEditor] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);

  // Use external state if provided, otherwise use local state
  const selectedImage = externalSelectedImage !== undefined ? externalSelectedImage : localSelectedImage;
  const setSelectedImage = externalSetSelectedImage || setLocalSelectedImage;
  const showImageEditor = externalShowImageEditor !== undefined ? externalShowImageEditor : localShowImageEditor;
  const setShowImageEditor = externalSetShowImageEditor || setLocalShowImageEditor;
  
  const placeholder = customPlaceholder || "What would you like to know?";

  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;
      for (const item of Array.from(items)) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            setSelectedImage(file);
            setShowImageEditor(true);
            break;
          }
        }
      }
    };
    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [setSelectedImage, setShowImageEditor]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim() || selectedImage) {
      if (setIsChatVisible) {
        setIsChatVisible(true);
      }

      if (handleSendMessage && isChatVisible) {
        handleSendMessage(query, selectedImage || undefined);
      } else if (onSubmit) {
        onSubmit(query, selectedImage || undefined);
      }

      setQuery("");
      setSelectedImage(null);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.size === 0) {
        console.error('Empty image file selected:', file.name);
        alert('The selected image file is empty or invalid. Please select a different image.');
        return;
      }
      setSelectedImage(file);
      setShowImageEditor(true);
    }
  };

  const handleImageSend = (editedImage: File) => {
    if (editedImage.size === 0) {
      console.error('Empty image file after editing:', editedImage.name);
      alert('The edited image is empty or invalid. Please try again with a different image.');
      return;
    }

    setSelectedImage(editedImage);
    setShowImageEditor(false);
  };

  const handleDragOver = (event: React.DragEvent<HTMLFormElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        if (file.size === 0) {
          console.error('Empty image file dropped:', file.name);
          alert('The dropped image file is empty or invalid. Please select a different image.');
          return;
        }
        setSelectedImage(file);
        setShowImageEditor(true);
      }
    }
  };

  // Render modern input for chat interface
  if (isChatVisible) {
    return (
      <>
        <form onSubmit={handleSubmit} className="relative w-full" onDragOver={handleDragOver} onDrop={handleDrop}>
          <div className="relative flex items-center w-full">
            <Input
              type="text"
              placeholder={placeholder}
              className={cn(
                "w-full pr-12 text-sm rounded-lg bg-transparent",
                isInputFocused ? "border-primary/30" : "",
                customClassName
              )}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onFocus={() => setIsInputFocused(true)}
              onBlur={() => setIsInputFocused(false)}
              autoFocus={true}
            />
            
            <div className="absolute right-2 flex items-center gap-2">
              <input
                type="file"
                id="image-upload"
                className="hidden"
                accept="image/*"
                onChange={handleImageUpload}
              />
              
              {!selectedImage && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full hover:bg-primary/10"
                  onClick={() => document.getElementById('image-upload')?.click()}
                >
                  <Image className="h-4 w-4 text-muted-foreground" />
                </Button>
              )}
              
              <Button
                type="submit"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-full",
                  query.trim() || selectedImage
                    ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                )}
                disabled={!query.trim() && !selectedImage}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </form>

        {/* Image Editor */}
        {selectedImage && (
          <ImageEditor
            imageFile={selectedImage}
            onSend={handleImageSend}
            onCancel={() => {
              setShowImageEditor(false);
              setSelectedImage(null);
            }}
            open={showImageEditor}
          />
        )}
      </>
    );
  }

  // Original search bar for non-chat interface
  return (
    <>
      <div className="flex flex-col md:flex-row md:items-center gap-3 md:gap-4 w-full max-w-full overflow-hidden">
        <form onSubmit={handleSubmit} className="relative w-full max-w-full" onDragOver={handleDragOver} onDrop={handleDrop}>
          <div className="relative flex items-center overflow-hidden w-full max-w-full">
            <div className="absolute left-3 sm:left-4 md:left-5 z-10 text-muted-foreground">
              <Search className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 opacity-70" />
            </div>
            <Input
              type="text"
              placeholder={placeholder}
              className="w-full pl-8 sm:pl-10 md:pl-12 pr-16 sm:pr-28 md:pr-40 py-4 sm:py-5 md:py-6 text-base sm:text-lg rounded-full border border-primary/10 bg-primary/5 backdrop-blur-sm hover:bg-primary/10 focus:bg-white/5 focus:border-primary/30 text-black dark:text-white/90 font-medium transition-all duration-200 shadow-lg truncate search-input max-w-full"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              autoFocus={true}
            />
            <div className="absolute right-0 flex items-center h-full">
              <input
                type="file"
                id="image-upload"
                className="hidden"
                accept="image/*"
                onChange={handleImageUpload}
              />
              <div className="flex items-center gap-1 sm:gap-2 md:gap-3 bg-black/20 backdrop-blur-md rounded-full px-1 sm:px-2 md:px-3 py-1 sm:py-1.5 md:py-2 border border-white/10 h-full">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className={`w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 rounded-full flex items-center justify-center transition-all duration-200 ${
                    selectedImage
                      ? 'text-primary bg-primary/20 border border-primary/30'
                      : 'text-black/70 hover:text-black hover:bg-cream-100/50 border-cream-200 dark:text-white/70 dark:hover:text-white dark:hover:bg-white/10 dark:border-transparent'
                  }`}
                  onClick={() => document.getElementById('image-upload')?.click()}
                  title="Upload image"
                >
                  <Image className="h-3.5 w-3.5 sm:h-4 sm:w-4 md:h-5 md:w-5" />
                </Button>
                <Button
                  type="submit"
                  className="rounded-full bg-gradient-to-r from-primary/80 to-primary hover:from-primary hover:to-primary/80 dark:from-primary/80 dark:to-primary dark:hover:from-primary dark:hover:to-primary/80 from-cream-300 to-cream-200 hover:from-cream-400 hover:to-cream-300 px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 h-7 sm:h-8 md:h-9 text-xs md:text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 dark:text-black text-black/80"
                >
                  <Search className="h-3.5 w-3.5 block sm:hidden" />
                  <span className="hidden sm:block">Search</span>
                </Button>
              </div>
            </div>
          </div>
          {selectedImage && !showImageEditor && (
            <div className="mt-3 p-3 bg-black/30 backdrop-blur-md rounded-xl border border-primary/20 shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center border border-primary/20">
                    <Image className="h-4 w-4 text-primary/70" />
                  </div>
                  <p className="text-xs md:text-sm text-white/80 truncate max-w-[75%]">
                    <span className="font-medium">{selectedImage.name}</span>
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 md:h-7 md:w-7 rounded-full p-0 bg-white/5 hover:bg-white/10 text-white/60 hover:text-white/90 transition-all duration-200"
                  onClick={() => setSelectedImage(null)}
                >
                  <X className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
              </div>
            </div>
          )}
        </form>

        {!isChatVisible && (
          <div className="hidden md:block">
            <Tools onToolOutput={onToolOutput} />
          </div>
        )}
      </div>

      {/* Image Editor */}
      {selectedImage && (
        <ImageEditor
          imageFile={selectedImage}
          onSend={handleImageSend}
          onCancel={() => {
            setShowImageEditor(false);
            setSelectedImage(null);
          }}
          open={showImageEditor}
        />
      )}
    </>
  );
};
