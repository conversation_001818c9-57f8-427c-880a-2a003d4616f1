import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ClipboardList, Clock, Calendar, Pencil, Trash2, X, Check, Copy } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import html2canvas from 'html2canvas';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { getTaskTypeEmoji } from "@/utils/studyUtils";

// Interface for timeline sessions
interface TimelineSession {
  id: string;
  subject: string;
  taskType: string;
  taskDescription: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  subjectColor?: string;
  productivityRating?: number;
}

interface StudySessionsViewProps {
  dailySessions?: { 
    date: string;
    sessions: TimelineSession[];
  }[];
  selectedDate: string;
  formatDuration: (seconds: number) => string;
  title?: string;
  onEditSession?: (sessionId: string, updatedSession: Partial<TimelineSession>) => Promise<boolean>;
  onDeleteSession?: (sessionId: string) => Promise<boolean>;
  subjects?: string[];
  taskTypes?: string[];
}

const formatTime = (date: Date): string => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return 'Invalid time';
  }
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Function to get the productivity rating emoji
const getProductivityEmoji = (rating?: number): string => {
  if (!rating) return "";
  
  switch(rating) {
    case 1: return "😔";
    case 2: return "😐";
    case 3: return "🙂";
    case 4: return "😊";
    case 5: return "🤩";
    default: return "";
  }
};

const StudySessionsView: React.FC<StudySessionsViewProps> = ({
  dailySessions = [],
  selectedDate,
  formatDuration,
  title = "Study Sessions",
  onEditSession,
  onDeleteSession,
  subjects = [],
  taskTypes = ["Study", "Homework", "Revision", "Test", "Project", "Research", "Other"]
}) => {
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<TimelineSession>>({});
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Get the sessions for the selected date
  const selectedDaySessions = dailySessions.find(day => day.date === selectedDate)?.sessions || [];
  
  // Group sessions by subject for better organization
  const sessionsBySubject = selectedDaySessions.reduce((acc: {[key: string]: TimelineSession[]}, session) => {
    if (!acc[session.subject]) {
      acc[session.subject] = [];
    }
    acc[session.subject].push(session);
    return acc;
  }, {});

  const handleEditClick = (session: TimelineSession) => {
    setEditingSessionId(session.id);
    setEditFormData({
      subject: session.subject,
      taskType: session.taskType,
      taskDescription: session.taskDescription,
      startTime: session.startTime,
      endTime: session.endTime,
    });
  };

  const handleSaveEdit = async (sessionId: string) => {
    if (!onEditSession || !editFormData) return;
    
    setIsProcessing(true);
    try {
      const success = await onEditSession(sessionId, editFormData);
      if (success) {
        setEditingSessionId(null);
        setEditFormData({});
      }
    } catch (error) {
      console.error("Error saving session edit:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingSessionId(null);
    setEditFormData({});
  };

  const handleDeleteClick = (sessionId: string) => {
    setIsDeleting(sessionId);
  };

  const handleConfirmDelete = async () => {
    if (!onDeleteSession || !isDeleting) return;
    
    setIsProcessing(true);
    try {
      const success = await onDeleteSession(isDeleting);
      if (success) {
        setIsDeleting(null);
      }
    } catch (error) {
      console.error("Error deleting session:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // New function to copy study session data to clipboard
  const copySessionsToClipboard = async () => {
    try {
      if (Object.keys(sessionsBySubject).length === 0) {
        toast({
          title: "No sessions to copy",
          description: "There are no study sessions recorded for this date.",
          variant: "destructive"
        });
        return;
      }

      let copyText = "";
      
      // Format sessions for each subject
      Object.entries(sessionsBySubject).forEach(([subject, sessions]) => {
        copyText += `${subject}\n`;
        
        sessions
          .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
          .forEach((session) => {
            copyText += `${formatTime(session.startTime)} to ${formatTime(session.endTime)} (${formatDuration(session.duration)}): ${session.taskType} - ${session.taskDescription || 'No description'}\n`;
          });
        
        copyText += "\n";
      });
      
      // Add signature
      copyText += "~ Powered by IsotopeAI";
      
      // Copy to clipboard
      await navigator.clipboard.writeText(copyText);
      
      toast({
        title: "Copied to clipboard!",
        description: "Your study sessions have been copied to your clipboard.",
      });
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast({
        title: "Copy failed",
        description: "There was an error copying to clipboard.",
        variant: "destructive"
      });
    }
  };

  return (
    <>
      <Card id="study-sessions-card" className="bg-card text-card-foreground border shadow-xl h-full overflow-hidden">
        <CardHeader className="border-b pb-4">
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl font-medium flex items-center gap-2">
              <ClipboardList className="h-5 w-5 text-primary" />
              <span>{title}</span>
            </CardTitle>
            <button
              onClick={copySessionsToClipboard}
              className="p-1.5 rounded-lg bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground transition-colors"
              aria-label="Copy to clipboard"
              title="Copy to clipboard"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </CardHeader>
        
        <CardContent className="pt-6 overflow-y-auto max-h-[500px] custom-scrollbar">
          {Object.keys(sessionsBySubject).length > 0 ? (
            <div className="space-y-6">
              {Object.entries(sessionsBySubject).map(([subject, sessions]) => (
                <div key={subject} className="bg-muted/30 rounded-xl border p-4 shadow-sm">
                  <div className="flex items-center gap-2 mb-3 pb-2 border-b">
                    <div 
                      className="w-8 h-8 rounded-lg flex items-center justify-center"
                      style={{ backgroundColor: `${sessions[0].subjectColor || '#6366f1'}20` }}
                    >
                      <div
                        className="w-3 h-3 rounded-md"
                        style={{ backgroundColor: sessions[0].subjectColor || '#6366f1' }}
                      />
                    </div>
                    <h3 className="font-semibold text-lg">{subject}</h3>
                  </div>
                  
                  <div className="space-y-3">
                    {sessions
                      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
                      .map((session) => (
                        <div 
                          key={session.id} 
                          className="bg-card/60 backdrop-blur-sm p-3 rounded-lg border flex flex-col gap-2 shadow-sm hover:shadow-md transition-shadow"
                        >
                          {editingSessionId === session.id ? (
                            // Edit form
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <div className="flex space-x-2 text-xs">
                                  <input
                                    type="time"
                                    className="p-1.5 bg-muted rounded border border-border"
                                    value={editFormData.startTime ? new Date(editFormData.startTime).toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }) : ''}
                                    onChange={(e) => {
                                      const [hours, minutes] = e.target.value.split(':').map(Number);
                                      const newDate = new Date(session.startTime);
                                      newDate.setHours(hours, minutes);
                                      setEditFormData({ ...editFormData, startTime: newDate });
                                    }}
                                  />
                                  <span className="self-center">-</span>
                                  <input
                                    type="time"
                                    className="p-1.5 bg-muted rounded border border-border"
                                    value={editFormData.endTime ? new Date(editFormData.endTime).toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }) : ''}
                                    onChange={(e) => {
                                      const [hours, minutes] = e.target.value.split(':').map(Number);
                                      const newDate = new Date(session.endTime);
                                      newDate.setHours(hours, minutes);
                                      setEditFormData({ ...editFormData, endTime: newDate });
                                    }}
                                  />
                                </div>
                                <span className="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded-full">
                                  {formatDuration(session.duration)}
                                </span>
                              </div>
                              
                              <div className="grid grid-cols-2 gap-2">
                                <Select
                                  value={editFormData.subject}
                                  onValueChange={(value) => setEditFormData({ ...editFormData, subject: value })}
                                >
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue placeholder="Subject" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {subjects.length > 0 ? 
                                      subjects.map(subj => (
                                        <SelectItem key={subj} value={subj} className="text-xs">
                                          {subj}
                                        </SelectItem>
                                      )) : 
                                      <SelectItem value={session.subject} className="text-xs">
                                        {session.subject}
                                      </SelectItem>
                                    }
                                  </SelectContent>
                                </Select>
                                
                                <Select
                                  value={editFormData.taskType}
                                  onValueChange={(value) => setEditFormData({ ...editFormData, taskType: value })}
                                >
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue placeholder="Task Type" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {taskTypes.map(type => (
                                      <SelectItem key={type} value={type} className="text-xs">
                                        {type}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <Textarea
                                placeholder="Task description"
                                className="h-16 text-xs resize-none"
                                value={editFormData.taskDescription || ''}
                                onChange={(e) => setEditFormData({ ...editFormData, taskDescription: e.target.value })}
                              />
                              
                              <div className="flex justify-end space-x-2 pt-1">
                                <Button 
                                  size="sm" 
                                  variant="outline" 
                                  className="h-7 px-2" 
                                  onClick={handleCancelEdit}
                                  disabled={isProcessing}
                                >
                                  <X className="h-3.5 w-3.5 mr-1" />
                                  Cancel
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="default" 
                                  className="h-7 px-2" 
                                  onClick={() => handleSaveEdit(session.id)}
                                  disabled={isProcessing}
                                >
                                  <Check className="h-3.5 w-3.5 mr-1" />
                                  Save
                                </Button>
                              </div>
                            </div>
                          ) : (
                            // Regular view
                            <>
                              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                                <div className="flex-shrink-0 bg-background/80 px-3 py-1.5 rounded-md border flex items-center gap-2">
                                  <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                                  <span className="text-sm font-medium">{formatTime(session.startTime)} - {formatTime(session.endTime)}</span>
                                  <span className="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded-full">
                                    {formatDuration(session.duration)}
                                  </span>
                                </div>
                                
                                <div className="flex-1 flex justify-between items-center">
                                  <div className="flex flex-wrap gap-2 items-center">
                                    <span className="text-sm font-medium bg-muted px-2 py-0.5 rounded-md">
                                      {getTaskTypeEmoji(session.taskType)} {session.taskType || 'Study'}
                                    </span>
                                    {session.productivityRating && (
                                      <span className="text-sm font-medium bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30 text-amber-700 dark:text-amber-300 px-2 py-0.5 rounded-md flex items-center" title={`Productivity: ${session.productivityRating}/5`}>
                                        <span className="mr-1">Rating:</span> {getProductivityEmoji(session.productivityRating)}
                                      </span>
                                    )}
                                    {session.taskDescription && (
                                      <p className="text-sm text-foreground">{session.taskDescription}</p>
                                    )}
                                  </div>
                                  
                                  {/* Edit/Delete buttons */}
                                  {(onEditSession || onDeleteSession) && (
                                    <div className="flex space-x-2 ml-2 flex-shrink-0">
                                      {onEditSession && (
                                        <Button 
                                          size="sm" 
                                          variant="ghost" 
                                          className="h-6 px-2 text-muted-foreground hover:text-foreground" 
                                          onClick={() => handleEditClick(session)}
                                        >
                                          <Pencil className="h-3.5 w-3.5" />
                                        </Button>
                                      )}
                                      {onDeleteSession && (
                                        <Button 
                                          size="sm" 
                                          variant="ghost" 
                                          className="h-6 px-2 text-muted-foreground hover:text-destructive" 
                                          onClick={() => handleDeleteClick(session.id)}
                                        >
                                          <Trash2 className="h-3.5 w-3.5" />
                                        </Button>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </>
                          )}
                        </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-center">
              <Calendar className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
              <p className="text-muted-foreground">No study sessions recorded for this date.</p>
              <p className="text-xs text-muted-foreground mt-2">Start a study session to see it here.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Dialog for Delete */}
      <AlertDialog open={!!isDeleting} onOpenChange={(open) => !open && setIsDeleting(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Study Session</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this study session? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleConfirmDelete}
              disabled={isProcessing}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isProcessing ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default StudySessionsView; 