import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"; // Assuming shadcn/ui dialog is used
import { Button } from "@/components/ui/button";
import { DailyStat } from './StudyCalendar'; // Assuming DailyStat type is exported or defined elsewhere accessible
import { Pencil, Trash2, X, Check } from 'lucide-react'; // Import icons
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Add session interface to show detailed session info
interface SessionDetail {
  id: string;
  subject: string;
  taskType: string;
  taskDescription: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  subjectColor?: string;
}

interface DayDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | null;
  studyData: DailyStat | undefined;
  sessionDetails?: SessionDetail[]; // Add session details prop
  formatDuration: (seconds: number) => string; // Pass the formatter
  subjectColorMap?: { [subject: string]: string }; // Optional subject color map
  onEditSession?: (sessionId: string, updatedSession: Partial<SessionDetail>) => Promise<boolean>; // Add edit handler
  onDeleteSession?: (sessionId: string) => Promise<boolean>; // Add delete handler
  subjects?: string[]; // Available subjects for dropdown
  taskTypes?: string[]; // Available task types for dropdown
}

const DayDetailModal: React.FC<DayDetailModalProps> = ({
  isOpen,
  onClose,
  selectedDate,
  studyData,
  sessionDetails = [], // Default to empty array
  formatDuration,
  subjectColorMap = {}, // Default color map
  onEditSession,
  onDeleteSession,
  subjects = [],
  taskTypes = ["Study", "Homework", "Revision", "Test", "Project", "Research", "Other"],
}) => {
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<SessionDetail>>({});
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen || !selectedDate) {
    return null;
  }

  const formattedDate = selectedDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Group sessions by subject
  const sessionsBySubject = sessionDetails.reduce((acc, session) => {
    if (!acc[session.subject]) {
      acc[session.subject] = [];
    }
    acc[session.subject].push(session);
    return acc;
  }, {} as Record<string, SessionDetail[]>);

  const handleEditClick = (session: SessionDetail) => {
    setEditingSessionId(session.id);
    setEditFormData({
      subject: session.subject,
      taskType: session.taskType,
      taskDescription: session.taskDescription,
      startTime: session.startTime,
      endTime: session.endTime,
    });
  };

  const handleSaveEdit = async (sessionId: string) => {
    if (!onEditSession || !editFormData) return;
    
    setIsProcessing(true);
    try {
      const success = await onEditSession(sessionId, editFormData);
      if (success) {
        setEditingSessionId(null);
        setEditFormData({});
      }
    } catch (error) {
      console.error("Error saving session edit:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingSessionId(null);
    setEditFormData({});
  };

  const handleDeleteClick = (sessionId: string) => {
    setIsDeleting(sessionId);
  };

  const handleConfirmDelete = async () => {
    if (!onDeleteSession || !isDeleting) return;
    
    setIsProcessing(true);
    try {
      const success = await onDeleteSession(isDeleting);
      if (success) {
        setIsDeleting(null);
      }
    } catch (error) {
      console.error("Error deleting session:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        {/* Use standard DialogContent styling which adapts to theme */}
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold text-foreground">Study Details</DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Details for {formattedDate}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4 text-sm text-foreground"> {/* Use foreground color */}
            {studyData && studyData.totalDuration > 0 ? (
              <>
                <p><span className="font-medium text-foreground/90">Total Time:</span> {formatDuration(studyData.totalDuration)}</p>
                {Object.keys(studyData.subjectDurations).length > 0 && (
                  <div>
                    <p className="font-medium text-foreground/90 mb-1">Subject Breakdown:</p>
                    <ul className="space-y-1 text-muted-foreground"> {/* Use muted foreground for list items */}
                      {Object.entries(studyData.subjectDurations)
                        // Explicitly type the destructured duration as number
                        .filter(([, duration]: [string, number]) => duration > 0)
                        .map(([subject, duration]: [string, number]) => {
                          const color = subjectColorMap[subject] || '#a1a1aa'; // Default to gray
                          return (
                            <li key={subject} className="flex items-center gap-2">
                               <span className="h-2.5 w-2.5 rounded-full inline-block" style={{ backgroundColor: color }}></span>
                               <span>{subject}: {formatDuration(duration)}</span>
                            </li>
                          );
                        })}
                    </ul>
                  </div>
                )}
                <p><span className="font-medium text-foreground/90">Completed Pomodoros:</span> {studyData.completedPomodoros}</p>
                
                {/* Session Details Section */}
                {sessionDetails.length > 0 && (
                  <div className="mt-4">
                    <p className="font-medium text-foreground/90 mb-2">Session Details:</p>
                    <div className="max-h-[300px] overflow-y-auto pr-2">
                      {Object.entries(sessionsBySubject)
                        .sort(([, sessionsA], [, sessionsB]) => {
                          // Calculate total duration for subject A
                          const totalDurationA = sessionsA.reduce((total, session) => total + session.duration, 0);
                          // Calculate total duration for subject B
                          const totalDurationB = sessionsB.reduce((total, session) => total + session.duration, 0);
                          // Sort by total duration (descending)
                          return totalDurationB - totalDurationA;
                        })
                        .map(([subject, sessions]) => (
                        <div key={subject} className="mb-3">
                          <div className="flex items-center gap-2 mb-1">
                            <span 
                              className="h-2.5 w-2.5 rounded-full inline-block" 
                              style={{ backgroundColor: subjectColorMap[subject] || '#a1a1aa' }}
                            ></span>
                            <span className="font-semibold">{subject}</span>
                          </div>
                          
                          <ul className="space-y-2 pl-4">
                            {sessions
                              .sort((a, b) => a.startTime.getTime() - b.startTime.getTime()) // Sort by start time
                              .map((session, index) => (
                              <li key={session.id || `session-${index}`} className="bg-muted/30 rounded-md p-2 text-foreground/90">
                                {editingSessionId === session.id ? (
                                  // Edit form
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <div className="flex space-x-2 text-xs">
                                        <input
                                          type="time"
                                          className="p-1 bg-muted rounded border border-border"
                                          value={editFormData.startTime ? new Date(editFormData.startTime).toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }) : ''}
                                          onChange={(e) => {
                                            const [hours, minutes] = e.target.value.split(':').map(Number);
                                            const newDate = new Date(session.startTime);
                                            newDate.setHours(hours, minutes);
                                            setEditFormData({ ...editFormData, startTime: newDate });
                                          }}
                                        />
                                        <span className="self-center">-</span>
                                        <input
                                          type="time"
                                          className="p-1 bg-muted rounded border border-border"
                                          value={editFormData.endTime ? new Date(editFormData.endTime).toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }) : ''}
                                          onChange={(e) => {
                                            const [hours, minutes] = e.target.value.split(':').map(Number);
                                            const newDate = new Date(session.endTime);
                                            newDate.setHours(hours, minutes);
                                            setEditFormData({ ...editFormData, endTime: newDate });
                                          }}
                                        />
                                      </div>
                                      <span>{formatDuration(session.duration)}</span>
                                    </div>
                                    
                                    <div className="grid grid-cols-2 gap-2">
                                      <Select
                                        value={editFormData.subject}
                                        onValueChange={(value) => setEditFormData({ ...editFormData, subject: value })}
                                      >
                                        <SelectTrigger className="h-8 text-xs">
                                          <SelectValue placeholder="Subject" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {subjects.length > 0 ? 
                                            subjects.map(subject => (
                                              <SelectItem key={subject} value={subject} className="text-xs">
                                                {subject}
                                              </SelectItem>
                                            )) : 
                                            <SelectItem value={session.subject} className="text-xs">
                                              {session.subject}
                                            </SelectItem>
                                          }
                                        </SelectContent>
                                      </Select>
                                      
                                      <Select
                                        value={editFormData.taskType}
                                        onValueChange={(value) => setEditFormData({ ...editFormData, taskType: value })}
                                      >
                                        <SelectTrigger className="h-8 text-xs">
                                          <SelectValue placeholder="Task Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {taskTypes.map(type => (
                                            <SelectItem key={type} value={type} className="text-xs">
                                              {type}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                    
                                    <Textarea
                                      placeholder="Task description"
                                      className="h-16 text-xs resize-none"
                                      value={editFormData.taskDescription || ''}
                                      onChange={(e) => setEditFormData({ ...editFormData, taskDescription: e.target.value })}
                                    />
                                    
                                    <div className="flex justify-end space-x-2 pt-1">
                                      <Button 
                                        size="sm" 
                                        variant="outline" 
                                        className="h-7 px-2" 
                                        onClick={handleCancelEdit}
                                        disabled={isProcessing}
                                      >
                                        <X className="h-3.5 w-3.5 mr-1" />
                                        Cancel
                                      </Button>
                                      <Button 
                                        size="sm" 
                                        variant="default" 
                                        className="h-7 px-2" 
                                        onClick={() => handleSaveEdit(session.id)}
                                        disabled={isProcessing}
                                      >
                                        <Check className="h-3.5 w-3.5 mr-1" />
                                        Save
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  // Regular view
                                  <>
                                    <div className="flex justify-between text-xs mb-1">
                                      <span>{session.startTime.toLocaleTimeString()} - {session.endTime.toLocaleTimeString()}</span>
                                      <span>{formatDuration(session.duration)}</span>
                                    </div>
                                    <div className="text-sm">
                                      <span className="font-medium">{session.taskType}</span>
                                      {session.taskDescription && (
                                        <p className="text-xs text-muted-foreground mt-1">{session.taskDescription}</p>
                                      )}
                                    </div>
                                    {/* Edit/Delete buttons */}
                                    {(onEditSession || onDeleteSession) && (
                                      <div className="flex justify-end mt-2 space-x-2">
                                        {onEditSession && (
                                          <Button 
                                            size="sm" 
                                            variant="ghost" 
                                            className="h-6 px-2 text-muted-foreground hover:text-foreground" 
                                            onClick={() => handleEditClick(session)}
                                          >
                                            <Pencil className="h-3.5 w-3.5" />
                                          </Button>
                                        )}
                                        {onDeleteSession && (
                                          <Button 
                                            size="sm" 
                                            variant="ghost" 
                                            className="h-6 px-2 text-muted-foreground hover:text-destructive" 
                                            onClick={() => handleDeleteClick(session.id)}
                                          >
                                            <Trash2 className="h-3.5 w-3.5" />
                                          </Button>
                                        )}
                                      </div>
                                    )}
                                  </>
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <p className="text-muted-foreground">No study sessions recorded for this day.</p>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog for Delete */}
      <AlertDialog open={!!isDeleting} onOpenChange={(open) => !open && setIsDeleting(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Study Session</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this study session? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleConfirmDelete}
              disabled={isProcessing}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isProcessing ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DayDetailModal;
// Re-export DailyStat if it's defined in StudyCalendar and not globally
export type { DailyStat };
