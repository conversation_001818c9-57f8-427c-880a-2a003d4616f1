import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Calendar, Camera } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import html2canvas from 'html2canvas';

interface Analytics {
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  subjectStats: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
    averageSessionDuration: number;
  }[];
}

interface DailySubjectOverview {
  subject: string;
  duration: number;
  percentageOfDay: number;
  date: string;
  color: string;
}

interface DailyOverviewProps {
  dailyStats: Analytics['dailyStats'];
  subjectStats: Analytics['subjectStats'];
  selectedDate: string;
  onDateChange: (date: string) => void;
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
}

const DailyOverview: React.FC<DailyOverviewProps> = ({
  dailyStats,
  subjectStats,
  selectedDate,
  onDateChange,
  formatDuration,
  subjectColorMap
}) => {
  // Find the selected day's data
  const selectedDayData = dailyStats.find(day => day.date === selectedDate) || dailyStats[dailyStats.length - 1];

  // If we have no data yet, show a message
  if (!selectedDayData) {
    return (
      <Card className="bg-card text-card-foreground border shadow-md h-full">
        <CardContent className="pt-6 flex flex-col items-center justify-center h-full">
          <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
            <Calendar className="w-8 h-8 text-muted-foreground" />
          </div>
          <p className="text-center text-muted-foreground">No study data available yet.</p>
        </CardContent>
      </Card>
    );
  }

  // Create overview data for each subject studied that day
  const subjectOverviews: DailySubjectOverview[] = Object.entries(selectedDayData.subjectDurations)
    .filter(([_, duration]) => duration > 0)
    .map(([subject, duration]) => ({
      subject,
      duration,
      percentageOfDay: selectedDayData.totalDuration > 0 ? (duration / selectedDayData.totalDuration) * 100 : 0,
      date: selectedDayData.date,
      color: subjectColorMap[subject] || '#6366f1'
    }))
    .sort((a, b) => b.duration - a.duration);

  const captureScreenshot = async () => {
    try {
      const element = document.getElementById('daily-overview-card');
      if (!element) return;

      // Capture the element as it is rendered, respecting theme styles
      // Ensure styles are fully applied before capturing
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay for styles

      const canvas = await html2canvas(element, {
          useCORS: true, // Allow loading cross-origin images if any
          // Let html2canvas determine the background from the element's computed style
          background: window.getComputedStyle(element).backgroundColor
      });

      const dataUrl = canvas.toDataURL('image/png');

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = `daily-overview-${selectedDate}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Screenshot captured!",
        description: "Your daily overview has been saved.",
      });
    } catch (error) {
      console.error("Error capturing screenshot:", error);
      toast({
        title: "Screenshot failed",
        description: "There was an error capturing the screenshot.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card id="daily-overview-card" className="bg-card text-card-foreground border shadow-md h-full overflow-hidden">
      <CardHeader className="border-b pb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl font-medium flex items-center gap-2">
            <Calendar className="h-5 w-5 text-primary" />
            <span>Daily Subject Overview</span>
          </CardTitle>
          <button
            onClick={captureScreenshot}
            className="p-1.5 rounded-lg bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground transition-colors"
            aria-label="Capture screenshot"
            title="Capture screenshot"
          >
            <Camera className="h-4 w-4" />
          </button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 pt-6 text-card-foreground">
        {/* Main stats in one row */}
        <div className="grid grid-cols-3 gap-4">
          {/* Use theme colors, maybe primary/secondary/accent */}
          <div className="bg-primary/10 rounded-xl p-4 border border-primary/20 shadow-md">
            <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1">
              <span className="w-2 h-2 rounded-full bg-indigo-400"></span>
              Total Study Time
            </h3>
            <p className="text-2xl font-bold text-indigo-400">{formatDuration(selectedDayData.totalDuration)}</p>
          </div>
          <div className="bg-pink-500/10 rounded-xl p-4 border border-pink-500/20 shadow-md">
            <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1">
              <span className="w-2 h-2 rounded-full bg-pink-500"></span>
              Completed Pomodoros
            </h3>
            <p className="text-2xl font-bold text-pink-500">{selectedDayData.completedPomodoros}</p>
          </div>
          <div className="bg-cyan-500/10 rounded-xl p-4 border border-cyan-500/20 shadow-md">
            <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1">
              <span className="w-2 h-2 rounded-full bg-cyan-500"></span>
              Subjects Studied
            </h3>
            <p className="text-2xl font-bold text-cyan-500">{subjectOverviews.length}</p>
          </div>
        </div>

        {/* Subjects list in separate rows */}
        {subjectOverviews.length > 0 ? (
          <div className="grid grid-cols-1 gap-2 max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
            {subjectOverviews.map((subject) => (
              <div
                key={subject.subject}
                className="flex items-center p-4 bg-muted/50 hover:bg-accent transition-colors rounded-xl border shadow-sm" // Updated classes
              >
                <div
                  className="w-10 h-10 rounded-lg mr-4 flex items-center justify-center"
                  style={{ backgroundColor: `${subject.color}20` }}
                >
                  <div
                    className="w-4 h-4 rounded-md"
                    style={{ backgroundColor: subject.color }}
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-foreground">{subject.subject}</h4>
                  <div className="mt-1 h-1.5 w-full bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full"
                      style={{
                        width: `${subject.percentageOfDay}%`,
                        backgroundColor: subject.color
                      }}
                    />
                  </div>
                </div>
                <div className="text-right ml-4">
                  <p className="text-sm font-medium text-foreground">{formatDuration(subject.duration)}</p>
                  <p className="text-xs text-muted-foreground">{subject.percentageOfDay.toFixed(1)}% of day</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 bg-muted/50 rounded-xl border">
            <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
              <Calendar className="w-6 h-6 text-muted-foreground" />
            </div>
            <p className="text-center text-muted-foreground">No subjects studied on this day.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DailyOverview;
