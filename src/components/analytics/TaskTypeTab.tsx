import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  BookCopy,
  <PERSON>Tool,
  <PERSON><PERSON>bell,
  Search,
  FileCog,
  BarChart3,
  Activity
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ueType,
  pieArcLabelClasses
} from '@mui/x-charts';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { secondsToHours } from '@/utils/timeUtils';

// Define props
interface TaskTypeTabProps {
  analytics: {
    taskTypeStats?: {
      taskType: string;
      totalDuration: number;
      sessionCount: number;
      averageSessionDuration: number;
      averageProductivityRating?: number;
    }[];
  };
  formatDuration: (seconds: number) => string;
  theme: 'light' | 'dark';
  muiTheme: any;
}

// Task type icon mapping
const getTaskTypeIcon = (taskType: string) => {
  switch (taskType.toLowerCase()) {
    case "lecture":
      return <BookOpen className="h-5 w-5" />;
    case "reading":
      return <BookCopy className="h-5 w-5" />;
    case "exercise":
      return <PenTool className="h-5 w-5" />;
    case "practice":
      return <Dumbbell className="h-5 w-5" />;
    case "review":
      return <Search className="h-5 w-5" />;
    case "study":
    default:
      return <FileCog className="h-5 w-5" />;
  }
};

// Task type color mapping
const TASK_TYPE_COLORS = {
  "lecture": "#4f46e5", // indigo
  "reading": "#0ea5e9", // sky
  "exercise": "#10b981", // emerald
  "practice": "#f59e0b", // amber
  "review": "#ef4444", // red
  "study": "#8b5cf6", // violet
  "default": "#6b7280", // gray
};

const getTaskTypeColor = (taskType: string, darkMode: boolean): string => {
  const baseColor = TASK_TYPE_COLORS[taskType.toLowerCase()] || TASK_TYPE_COLORS.default;
  return darkMode ? baseColor : baseColor;
};

const TaskTypeTab: React.FC<TaskTypeTabProps> = ({
  analytics,
  formatDuration,
  theme,
  muiTheme
}) => {
  // Use the theme prop directly instead of the hook
  const isDarkMode = theme === "dark";

  // Check if we have task type data
  const taskTypes = analytics.taskTypeStats || [];
  const hasTaskTypeData = taskTypes.length > 0;

  // Sort task types by duration (descending)
  const sortedTaskTypes = [...taskTypes].sort((a, b) => b.totalDuration - a.totalDuration);

  // Prepare data for bar chart
  const barChartData = sortedTaskTypes.map(taskType => ({
    value: secondsToHours(taskType.totalDuration), // Convert to hours
    label: taskType.taskType,
    color: getTaskTypeColor(taskType.taskType, isDarkMode)
  }));

  // Prepare data for pie chart
  const pieChartData = sortedTaskTypes.map(taskType => ({
    value: taskType.totalDuration,
    label: taskType.taskType,
    color: getTaskTypeColor(taskType.taskType, isDarkMode)
  }));

  // Calculate total duration across all task types
  const totalDuration = taskTypes.reduce((sum, taskType) => sum + taskType.totalDuration, 0);

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Bar Chart */}
        {hasTaskTypeData ? (
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                <span>Time Spent by Task Type</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <BarChart
                dataset={barChartData}
                series={[{
                  dataKey: 'value',
                  label: 'Hours',
                  valueFormatter: (value) => formatDuration(value * 3600),
                }]}
                xAxis={[{
                  dataKey: 'label',
                  scaleType: 'band',
                }]}
                margin={{ top: 10, bottom: 30, left: 40, right: 10 }}
                slotProps={{
                  legend: { hidden: true },
                }}
                colors={barChartData.map(item => item.color)}
                sx={{
                  '& .MuiChartsAxis-label': {
                    fill: isDarkMode ? '#e2e8f0' : '#1e293b',
                  },
                  '& .MuiChartsAxis-tick': {
                    stroke: isDarkMode ? '#475569' : '#94a3b8',
                  },
                  '& .MuiChartsAxis-line': {
                    stroke: isDarkMode ? '#475569' : '#94a3b8',
                  },
                  '& .MuiChartsGrid-line': {
                    stroke: isDarkMode ? '#334155' : '#e2e8f0',
                  },
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                <span>Time Spent by Task Type</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center h-[400px]">
              <p className="text-muted-foreground">No task type data available.</p>
            </CardContent>
          </Card>
        )}

        {/* Pie Chart */}
        {hasTaskTypeData ? (
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                <span>Task Type Distribution</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <PieChart
                series={[
                  {
                    data: pieChartData,
                    highlightScope: { faded: 'global', highlighted: 'item' },
                    faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                    valueFormatter: (item: PieValueType) => formatDuration(item.value),
                  },
                ]}
                slotProps={{
                  legend: {
                    direction: 'column',
                    position: { vertical: 'middle', horizontal: 'right' },
                    padding: 0,
                    itemMarkWidth: 10,
                    itemMarkHeight: 10,
                    markGap: 5,
                    itemGap: 10,
                  },
                }}
                margin={{ top: 10, bottom: 10, left: 10, right: 130 }}
                sx={{
                  [`& .${pieArcLabelClasses.root}`]: {
                    fill: isDarkMode ? '#e2e8f0' : '#1e293b',
                    fontSize: 12,
                  },
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                <span>Task Type Distribution</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center h-[400px]">
              <p className="text-muted-foreground">No task type data available.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Task Type Stats Table */}
      <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Task Type Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          {hasTaskTypeData ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Task Type</TableHead>
                  <TableHead>Time Spent</TableHead>
                  <TableHead>% of Total</TableHead>
                  <TableHead>Sessions</TableHead>
                  <TableHead>Avg. Duration</TableHead>
                  <TableHead>Productivity Rating</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedTaskTypes.map((taskType) => (
                  <TableRow key={taskType.taskType}>
                    <TableCell className="font-medium flex items-center gap-2">
                      <div className="p-1 rounded-full" style={{ background: getTaskTypeColor(taskType.taskType, isDarkMode) + "33" }}>
                        {getTaskTypeIcon(taskType.taskType)}
                      </div>
                      {taskType.taskType}
                    </TableCell>
                    <TableCell>{formatDuration(taskType.totalDuration)}</TableCell>
                    <TableCell>
                      {Math.round((taskType.totalDuration / totalDuration) * 100)}%
                    </TableCell>
                    <TableCell>{taskType.sessionCount}</TableCell>
                    <TableCell>{formatDuration(taskType.averageSessionDuration)}</TableCell>
                    <TableCell>
                      {taskType.averageProductivityRating 
                        ? `${taskType.averageProductivityRating.toFixed(1)} / 5.0` 
                        : "N/A"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No task type data available.</p>
              <p className="text-xs text-muted-foreground mt-2">
                Try tracking your study sessions with different task types.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TaskTypeTab; 