import React, { useMemo } from 'react'; // Import useMemo
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeProvider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/x-charts';
import { Clock, Timer, BarChart2, Chart<PERSON>ie, CalendarDays, TrendingUp, ListChecks, BrainCircuit, Focus, AlertCircle } from 'lucide-react'; // Add AlertCircle
import { AxisConfig, ChartsXAxis, ChartsYAxis } from '@mui/x-charts'; // Import chart axis types if needed

// Define colors array for fallback
const COLORS = ['#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#f97316', '#10b981', '#06b6d4', '#3b82f6'];

// Define interfaces for the data structures we expect
interface SubjectStat {
  subject: string;
  totalDuration: number;
  completedPomodoros: number;
  averageSessionDuration: number;
  // We might add rank and contribution here later if calculated in parent
}

interface DailyStat {
  date: string;
  totalDuration: number;
  subjectDurations: { [key: string]: number };
  completedPomodoros: number;
  taskTypeDurations?: { [key: string]: number };
}

interface WeeklyStat {
  weekNumber: number;
  year: number;
  totalDuration: number;
  subjectDurations: { [key: string]: number };
  completedPomodoros: number;
  taskTypeDurations?: { [key: string]: number };
}

interface MonthlyStat {
  month: string;
  year: number;
  monthKey: string;
  totalDuration: number;
  subjectDurations: { [key: string]: number };
  completedPomodoros: number;
  taskTypeDurations?: { [key: string]: number };
}

interface StudySession {
  id: string; // Assuming sessions passed down have an ID
  subject: string;
  duration: number;
  mode: "pomodoro" | "stopwatch";
  phase?: "work" | "shortBreak" | "longBreak"; // Optional phase
  completed?: boolean; // Optional completed status
  date: string;
  weekNumber?: number; // Optional
  month?: string; // Optional
  year?: number; // Optional
  startTime?: Date;
  endTime?: Date;
  taskName?: string;
  taskType: string;
  taskDescription?: string;
  focusRating?: "focused" | "neutral" | "distracted";
  notes?: string;
  subjectColor?: string;
  productivityRating?: number;
}

interface SubjectDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  subjectData: SubjectStat | null; // Data for the specific subject
  allSubjectStats: SubjectStat[]; // Needed to calculate rank and contribution
  dailyStats: DailyStat[]; // All daily stats for filtering
  weeklyStats: WeeklyStat[]; // All weekly stats for filtering
  monthlyStats: MonthlyStat[]; // All monthly stats for filtering
  allSessions: StudySession[]; // All sessions for detailed filtering
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
  theme: 'light' | 'dark';
  muiTheme: any; // MUI theme object
}

// Helper to get last N days/weeks/months keys/labels
const getLastNDays = (n: number): { dates: string[], labels: string[] } => {
  const dates: string[] = [];
  const labels: string[] = [];
  const today = new Date();
  for (let i = n - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    dates.push(`${year}-${month}-${day}`);
    labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
  }
  return { dates, labels };
};

// Add similar helpers for weeks and months if needed, or adapt existing logic

const SubjectDetailModal: React.FC<SubjectDetailModalProps> = ({
  isOpen,
  onClose,
  subjectData,
  allSubjectStats,
  dailyStats,
  weeklyStats,
  monthlyStats,
  allSessions,
  formatDuration,
  subjectColorMap,
  theme,
  muiTheme,
}) => {

  if (!isOpen || !subjectData) {
    return null;
  }

  const subjectName = subjectData.subject;
  const subjectColor = subjectColorMap[subjectName] || '#6366f1'; // Default color

  // --- Data Processing using useMemo ---

  const {
    timeRank,
    contributionPercent,
    subjectSessions,
    dailyTrendData,
    weeklyTrendData,
    monthlyTrendData,
    taskTypeDistributionData,
    modeDistributionData,
    productivityDistributionData,
    focusDistributionData,
    averageProductivity,
    recentSessions
  } = useMemo(() => {
    if (!subjectData) return {
      timeRank: 0, contributionPercent: '0.0', subjectSessions: [], dailyTrendData: { labels: [], data: [] },
      weeklyTrendData: { labels: [], data: [] }, monthlyTrendData: { labels: [], data: [] },
      taskTypeDistributionData: [], modeDistributionData: [], productivityDistributionData: [],
      focusDistributionData: [], averageProductivity: 0, recentSessions: []
    };

    // Calculate Rank & Contribution
    const sortedSubjects = [...allSubjectStats].sort((a, b) => b.totalDuration - a.totalDuration);
    const rank = sortedSubjects.findIndex(s => s.subject === subjectName) + 1;
    const totalOverallDuration = allSubjectStats.reduce((acc, s) => acc + s.totalDuration, 0);
    const contribution = totalOverallDuration > 0 ? ((subjectData.totalDuration / totalOverallDuration) * 100).toFixed(1) : '0.0';

    // Filter Sessions for this subject
    const sessions = allSessions.filter(s => s.subject === subjectName);

    // --- Trend Data ---
    // Daily (Last 30 days)
    const last30Days = getLastNDays(30);
    const dailyDataMap = new Map(dailyStats.map(d => [d.date, d.subjectDurations[subjectName] || 0]));
    const dailyData = last30Days.dates.map(date => (dailyDataMap.get(date) || 0) / 3600); // Convert to hours

    // Weekly (Last 12 weeks - simplified example, needs proper week calculation)
    // This requires a more robust week calculation based on year/weekNumber
    const weeklyDataMap = new Map(weeklyStats.map(w => [`${w.year}-W${w.weekNumber}`, w.subjectDurations[subjectName] || 0]));
    // Placeholder: Get last 12 weekly stats available for this subject
    const recentWeeklyStats = weeklyStats
        .filter(w => w.subjectDurations[subjectName])
        .sort((a, b) => (b.year * 53 + b.weekNumber) - (a.year * 53 + a.weekNumber)) // Sort recent first
        .slice(0, 12)
        .reverse(); // Show oldest first
    const weeklyLabels = recentWeeklyStats.map(w => `W${w.weekNumber}`);
    const weeklyData = recentWeeklyStats.map(w => (w.subjectDurations[subjectName] || 0) / 3600);

    // Monthly (Last 12 months)
    const monthlyDataMap = new Map(monthlyStats.map(m => [m.monthKey, m.subjectDurations[subjectName] || 0]));
     // Placeholder: Get last 12 monthly stats available for this subject
    const recentMonthlyStats = monthlyStats
        .filter(m => m.subjectDurations[subjectName])
        .sort((a, b) => b.monthKey.localeCompare(a.monthKey)) // Sort recent first
        .slice(0, 12)
        .reverse(); // Show oldest first
    const monthlyLabels = recentMonthlyStats.map(m => new Date(m.monthKey + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' }));
    const monthlyData = recentMonthlyStats.map(m => (m.subjectDurations[subjectName] || 0) / 3600);


    // --- Distribution Data ---
    const taskTypes: { [key: string]: number } = {};
    const modes: { [key: string]: number } = { pomodoro: 0, stopwatch: 0 };
    const productivities: { [key: number]: number } = {};
    const focuses: { [key: string]: number } = { focused: 0, neutral: 0, distracted: 0 };
    let totalProductivityRating = 0;
    let productivityCount = 0;

    sessions.forEach(s => {
      // Task Type
      const taskType = s.taskType || 'Other';
      taskTypes[taskType] = (taskTypes[taskType] || 0) + s.duration;

      // Mode
      modes[s.mode] = (modes[s.mode] || 0) + s.duration;

      // Productivity
      if (s.productivityRating && s.productivityRating > 0) {
        const rating = Math.round(s.productivityRating); // Round to nearest integer
        productivities[rating] = (productivities[rating] || 0) + 1; // Count occurrences
        totalProductivityRating += s.productivityRating;
        productivityCount++;
      }

      // Focus
      if (s.focusRating) {
        focuses[s.focusRating] = (focuses[s.focusRating] || 0) + 1; // Count occurrences
      }
    });

    const avgProd = productivityCount > 0 ? (totalProductivityRating / productivityCount) : 0;

    const taskTypeDist = Object.entries(taskTypes)
        .map(([type, duration], index) => ({ id: type, value: duration / 3600, label: type, color: subjectColorMap[type] || COLORS[index % COLORS.length] })) // Use different colors maybe?
        .filter(d => d.value > 0); // Filter out zero values

    const modeDist = Object.entries(modes)
        .map(([mode, duration]) => ({ id: mode, value: duration / 3600, label: mode.charAt(0).toUpperCase() + mode.slice(1) }))
        .filter(d => d.value > 0);

    const prodDist = Object.entries(productivities)
        .map(([rating, count]) => ({ rating: parseInt(rating), count }))
        .sort((a, b) => a.rating - b.rating); // Sort by rating

    const focusDist = Object.entries(focuses)
        .map(([focus, count]) => ({ id: focus, value: count, label: focus.charAt(0).toUpperCase() + focus.slice(1) }))
        .filter(d => d.value > 0);

    // Recent Sessions
    const recent = sessions
        .sort((a, b) => new Date(b.startTime || b.date).getTime() - new Date(a.startTime || a.date).getTime())
        .slice(0, 15); // Show last 15

    return {
      timeRank: rank,
      contributionPercent: contribution,
      subjectSessions: sessions,
      dailyTrendData: { labels: last30Days.labels, data: dailyData },
      weeklyTrendData: { labels: weeklyLabels, data: weeklyData },
      monthlyTrendData: { labels: monthlyLabels, data: monthlyData },
      taskTypeDistributionData: taskTypeDist,
      modeDistributionData: modeDist,
      productivityDistributionData: prodDist,
      focusDistributionData: focusDist,
      averageProductivity: avgProd,
      recentSessions: recent
    };

  }, [subjectData, allSubjectStats, dailyStats, weeklyStats, monthlyStats, allSessions, subjectName, subjectColorMap]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-card/90 backdrop-blur-lg border shadow-xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <div className="w-4 h-4 rounded-sm" style={{ backgroundColor: subjectColor }} />
            Detailed Stats: {subjectName}
          </DialogTitle>
          <DialogDescription>
            In-depth analysis of your study patterns for this subject.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 my-4">
          {/* Summary Cards */}
          <div className="p-4 bg-muted/30 rounded-lg border">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-muted-foreground">Total Time</span>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-lg font-semibold">{formatDuration(subjectData.totalDuration)}</p>
          </div>
          <div className="p-4 bg-muted/30 rounded-lg border">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-muted-foreground">Pomodoros</span>
              <Timer className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-lg font-semibold">{subjectData.completedPomodoros}</p>
          </div>
          <div className="p-4 bg-muted/30 rounded-lg border">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-muted-foreground">Avg. Session</span>
              <BarChart2 className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-lg font-semibold">{formatDuration(subjectData.averageSessionDuration)}</p>
          </div>
          <div className="p-4 bg-muted/30 rounded-lg border">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-muted-foreground">Time Rank</span>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-lg font-semibold">#{timeRank}</p>
          </div>
          <div className="p-4 bg-muted/30 rounded-lg border">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm text-muted-foreground">Contribution</span>
              <ChartPie className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-lg font-semibold">{contributionPercent}%</p>
          </div>
        </div>

        {/* Charts Section */}
        <div className="space-y-6">
          {/* Activity Trends */}
          <div className="p-4 bg-muted/20 rounded-lg border">
            <h3 className="text-md font-semibold mb-3 flex items-center gap-2"><CalendarDays className="h-5 w-5 text-primary" />Activity Trends</h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* Daily Trend */}
              <div className="h-[200px]">
                <h4 className="text-sm text-center text-muted-foreground mb-1">Daily (Hours, Last 30d)</h4>
                <ThemeProvider theme={muiTheme}>
                  {dailyTrendData.data.length > 0 ? (
                    <LineChart
                      height={180}
                      series={[{ data: dailyTrendData.data, showMark: false, color: subjectColor, area: true }]}
                      xAxis={[{ scaleType: 'point', data: dailyTrendData.labels, tickLabelStyle: { fontSize: 9 } }]}
                      yAxis={[{ min: 0 }]}
                      margin={{ top: 10, bottom: 25, left: 25, right: 10 }}
                      grid={{ horizontal: true }}
                      axisHighlight={{ x: 'none', y: 'none' }}
                      tooltip={{ trigger: 'axis' }}
                    />
                  ) : <p className="text-center text-xs text-muted-foreground pt-10">No daily data</p>}
                </ThemeProvider>
              </div>
              {/* Weekly Trend */}
              <div className="h-[200px]">
                <h4 className="text-sm text-center text-muted-foreground mb-1">Weekly (Hours, Last 12w)</h4>
                 <ThemeProvider theme={muiTheme}>
                  {weeklyTrendData.data.length > 0 ? (
                     <BarChart
                       height={180}
                       series={[{ data: weeklyTrendData.data, label: 'Hours', color: subjectColor }]}
                       xAxis={[{ scaleType: 'band', data: weeklyTrendData.labels, tickLabelStyle: { fontSize: 9 } }]}
                       yAxis={[{ min: 0 }]}
                       margin={{ top: 10, bottom: 25, left: 25, right: 10 }}
                       grid={{ horizontal: true }}
                       tooltip={{ trigger: 'item' }}
                     />
                  ) : <p className="text-center text-xs text-muted-foreground pt-10">No weekly data</p>}
                 </ThemeProvider> {/* Added missing closing tag */}
              </div>
              {/* Monthly Trend */}
              <div className="h-[200px]">
                <h4 className="text-sm text-center text-muted-foreground mb-1">Monthly (Hours, Last 12m)</h4>
                 <ThemeProvider theme={muiTheme}>
                   {monthlyTrendData.data.length > 0 ? (
                     <BarChart
                       height={180}
                       series={[{ data: monthlyTrendData.data, label: 'Hours', color: subjectColor }]}
                       xAxis={[{ scaleType: 'band', data: monthlyTrendData.labels, tickLabelStyle: { fontSize: 9, angle: -30, textAnchor: 'end' } }]}
                       yAxis={[{ min: 0 }]}
                       margin={{ top: 10, bottom: 40, left: 25, right: 10 }} // Increased bottom margin for angled labels
                       grid={{ horizontal: true }}
                       tooltip={{ trigger: 'item' }}
                     />
                   ) : <p className="text-center text-xs text-muted-foreground pt-10">No monthly data</p>}
                </ThemeProvider>
              </div>
            </div>
          </div>

          {/* Study Method & Performance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 bg-muted/20 rounded-lg border">
              <h3 className="text-md font-semibold mb-3 flex items-center gap-2"><ListChecks className="h-5 w-5 text-primary" />Study Method</h3>
              <div className="grid grid-cols-2 gap-4">
                 {/* Task Type Distribution */}
                 <div className="h-[150px]">
                   <h4 className="text-sm text-center text-muted-foreground mb-1">Task Types (Hours)</h4>
                   <ThemeProvider theme={muiTheme}>
                     {taskTypeDistributionData.length > 0 ? (
                       <PieChart
                         height={130}
                         series={[{
                           data: taskTypeDistributionData,
                           innerRadius: 20,
                           outerRadius: 50,
                           paddingAngle: 2,
                           cornerRadius: 3,
                           highlightScope: { faded: 'global', highlighted: 'item' },
                           faded: { innerRadius: 20, additionalRadius: -5, color: 'gray' },
                         }]}
                         margin={{ top: 5, bottom: 5, left: 5, right: 5 }}
                         tooltip={{ trigger: 'item' }}
                         // legend={{ hidden: true }} // Hide legend if too cluttered
                       />
                     ) : <p className="text-center text-xs text-muted-foreground pt-10">No task type data</p>}
                   </ThemeProvider>
                 </div>
                 {/* Mode Distribution */}
                 <div className="h-[150px]">
                   <h4 className="text-sm text-center text-muted-foreground mb-1">Modes (Hours)</h4>
                   <ThemeProvider theme={muiTheme}>
                     {modeDistributionData.length > 0 ? (
                       <PieChart
                         height={130}
                         series={[{
                           data: modeDistributionData,
                           innerRadius: 20,
                           outerRadius: 50,
                           paddingAngle: 2,
                           cornerRadius: 3,
                           highlightScope: { faded: 'global', highlighted: 'item' },
                           faded: { innerRadius: 20, additionalRadius: -5, color: 'gray' },
                         }]}
                         margin={{ top: 5, bottom: 5, left: 5, right: 5 }}
                         tooltip={{ trigger: 'item' }}
                         // legend={{ hidden: true }}
                       />
                     ) : <p className="text-center text-xs text-muted-foreground pt-10">No mode data</p>}
                   </ThemeProvider>
                 </div>
              </div>
               {/* TODO: Add list breakdown for task types/modes */}
            </div>

            <div className="p-4 bg-muted/20 rounded-lg border">
              <h3 className="text-md font-semibold mb-3 flex items-center gap-2"><BrainCircuit className="h-5 w-5 text-primary" />Performance</h3>
               <div className="flex flex-col items-center mt-2">
                 {/* Productivity Distribution */}
                 <div className="h-[150px] w-full">
                   <h4 className="text-sm text-center text-muted-foreground mb-1">Productivity (Sessions)</h4>
                   <ThemeProvider theme={muiTheme}>
                     {productivityDistributionData.length > 0 ? (
                       <BarChart
                         height={130}
                         series={[{ data: productivityDistributionData.map(p => p.count), label: 'Sessions', color: subjectColor }]}
                         xAxis={[{ scaleType: 'band', data: productivityDistributionData.map(p => p.rating.toString()), tickLabelStyle: { fontSize: 9 } }]}
                         yAxis={[{ min: 0 }]}
                         margin={{ top: 10, bottom: 20, left: 25, right: 10 }}
                         grid={{ horizontal: true }}
                         tooltip={{ trigger: 'item' }}
                       />
                     ) : <p className="text-center text-xs text-muted-foreground pt-10">No productivity data</p>}
                   </ThemeProvider>
                 </div>
                 {averageProductivity > 0 && (
                    <p className="text-xs text-muted-foreground mt-1">Avg: {averageProductivity.toFixed(1)} / 5</p>
                 )}
                 {/* Focus Distribution */}
                 <div className="h-[150px] w-full mt-4">
                   <h4 className="text-sm text-center text-muted-foreground mb-1">Focus (Sessions)</h4>
                   <ThemeProvider theme={muiTheme}>
                     {focusDistributionData.length > 0 ? (
                       <PieChart
                         height={130}
                         series={[{
                           data: focusDistributionData,
                           innerRadius: 20,
                           outerRadius: 50,
                           paddingAngle: 2,
                           cornerRadius: 3,
                           highlightScope: { faded: 'global', highlighted: 'item' },
                           faded: { innerRadius: 20, additionalRadius: -5, color: 'gray' },
                         }]}
                         margin={{ top: 5, bottom: 5, left: 5, right: 5 }}
                         tooltip={{ trigger: 'item' }}
                         // legend={{ hidden: true }}
                       />
                     ) : <p className="text-center text-xs text-muted-foreground pt-10">No focus data</p>}
                   </ThemeProvider> {/* Added missing closing tag */}
                 </div>
               </div>
            </div>
          </div>

          {/* Recent Sessions */}
          <div className="p-4 bg-muted/20 rounded-lg border">
            <h3 className="text-md font-semibold mb-3 flex items-center gap-2"><Focus className="h-5 w-5 text-primary" />Recent Sessions</h3>
            <div className="max-h-48 overflow-y-auto space-y-2 pr-2 custom-scrollbar">
              {recentSessions.length > 0 ? (
                recentSessions.map((session) => (
                  <div key={session.id} className="flex justify-between items-center text-xs p-2 bg-background/50 rounded border border-transparent hover:border-muted transition-colors">
                    <div className="flex-1 overflow-hidden mr-2">
                      <span className="font-medium">{new Date(session.startTime || session.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                      <span className="text-muted-foreground ml-2">{session.taskType}</span>
                      {session.taskDescription && (
                        <span className="text-muted-foreground italic ml-1 truncate block sm:inline"> - {session.taskDescription}</span>
                      )}
                    </div>
                    <span className="font-semibold whitespace-nowrap">{formatDuration(session.duration)}</span>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4 flex items-center justify-center gap-2">
                  <AlertCircle className="h-4 w-4" /> No recent sessions found.
                </p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectDetailModal;
