import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ThemeProvider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/x-charts';
import { Line<PERSON>hart } from '@mui/x-charts';
import { <PERSON>, Timer, BookOpen, FileBar<PERSON>hart, FolderOpenDot, GraduationCap, Trash2 } from "lucide-react";
import { BarChart2, ChartPie } from 'lucide-react';
import { useState, useEffect } from 'react'; // Import useEffect
import { useNavigate } from 'react-router-dom';
import SubjectDetailModal from './SubjectDetailModal'; // Import the new modal
import { getSubjectColor } from '@/constants/subjectColors';
import { secondsToHours } from '@/utils/timeUtils';

interface Analytics {
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  weeklyStats: {
    weekNumber: number;
    year: number;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  monthlyStats: {
    month: string;
    year: number;
    monthKey: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  subjectStats: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
    averageSessionDuration: number;
  }[];
}

// Define StudySession interface (assuming it's not globally available)
// Adapt this if it's imported from elsewhere
interface StudySession {
  id: string;
  subject: string;
  duration: number;
  mode: "pomodoro" | "stopwatch";
  phase?: "work" | "shortBreak" | "longBreak";
  completed?: boolean;
  date: string;
  weekNumber?: number;
  month?: string;
  year?: number;
  startTime?: Date;
  endTime?: Date;
  taskName?: string;
  taskType: string;
  taskDescription?: string;
  focusRating?: "focused" | "neutral" | "distracted";
  notes?: string;
  subjectColor?: string;
  productivityRating?: number;
}


interface SubjectsTabProps {
  analytics: Analytics;
  // Add prop for all sessions needed by the modal
  allSessions: StudySession[];
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
  theme: 'light' | 'dark'; // Correct the theme type
  muiTheme: any;
  handleDeleteSubject: (subjectName: string) => Promise<void>;
  deletingSubject: string | null;
}

const SubjectsTab: React.FC<SubjectsTabProps> = ({
  analytics,
  formatDuration,
  subjectColorMap,
  theme,
  muiTheme,
  handleDeleteSubject,
  deletingSubject,
  allSessions // Add allSessions to destructuring
}) => {
  // Use shared subject colors for consistency
  
  // Sort subjects by time (default sort order)
  const sortedSubjects = [...(analytics?.subjectStats || [])].sort((a, b) => b.totalDuration - a.totalDuration);
  
  // For navigation functionality (keep for handleStudySubject)
  const navigate = useNavigate();

  // State for the detailed stats modal
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  // Use the correct type for subjectStats from Analytics interface
  const [selectedSubjectData, setSelectedSubjectData] = useState<Analytics['subjectStats'][0] | null>(null);


  // Function to get recent activity data for a subject
  const getRecentActivityData = (subject: string) => {
    // Get the 7 most recent days that have data
    const recentDays = [...analytics.dailyStats]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 7)
      .reverse(); // Reverse to show in chronological order
    
    // Map days to activity values, with 0 if the subject wasn't studied
    const activityData = recentDays.map(day => {
      return day.subjectDurations[subject] ? secondsToHours(day.subjectDurations[subject]) : 0; // Convert to hours
    });
    
    // Get the day labels (e.g., Mon, Tue, etc.)
    const dayLabels = recentDays.map(day => {
      const date = new Date(day.date);
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    });
    
    return { data: activityData, labels: dayLabels };
  };
  
  // Function to navigate to productivity page with selected subject
  const handleStudySubject = (subject: string) => {
    navigate('/productivity', { state: { selectedSubject: subject } });
  };
  
  // Function to show detailed stats for a subject in a modal
  const handleViewDetailedStats = (subjectName: string) => {
    const subjectDetails = analytics?.subjectStats.find(s => s.subject === subjectName);
    if (subjectDetails) {
      setSelectedSubjectData(subjectDetails);
      setIsDetailModalOpen(true);
    } else {
      console.error("Could not find details for subject:", subjectName);
      // Optionally show a toast notification
    }
  };

  // No need to destructure allSessions again, it's done in the function signature

  return (
    <div className="mb-6">
      {/* Main Card */}
      <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
        <CardHeader className="border-b pb-3">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <GraduationCap className="h-5 w-5 text-primary" />
            <span>Subject Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Total Statistics */}
            <div className="lg:col-span-3 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="p-4 bg-primary/5 rounded-xl border shadow-sm flex flex-col">
                <h3 className="text-sm text-muted-foreground mb-1">Total Study Time</h3>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-bold text-primary">
                    {formatDuration(analytics?.subjectStats.reduce((acc, s) => acc + s.totalDuration, 0) || 0)}
                  </p>
                  <div className="p-2 bg-primary/10 rounded-full">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                </div>
              </div>

              <div className="p-4 bg-pink-500/5 rounded-xl border shadow-sm flex flex-col">
                <h3 className="text-sm text-muted-foreground mb-1">Total Pomodoros</h3>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-bold text-pink-500">
                    {analytics?.subjectStats.reduce((acc, s) => acc + s.completedPomodoros, 0) || 0}
                  </p>
                  <div className="p-2 bg-pink-500/10 rounded-full">
                    <Timer className="h-5 w-5 text-pink-500" />
                  </div>
                </div>
              </div>

              <div className="p-4 bg-cyan-500/5 rounded-xl border shadow-sm flex flex-col">
                <h3 className="text-sm text-muted-foreground mb-1">Total Subjects</h3>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-bold text-cyan-500">
                    {analytics?.subjectStats.length || 0}
                  </p>
                  <div className="p-2 bg-cyan-500/10 rounded-full">
                    <BookOpen className="h-5 w-5 text-cyan-500" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Subject Distribution Visualization */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <Card className="bg-card/60 backdrop-blur-sm border shadow-md rounded-xl overflow-hidden">
              <CardHeader className="border-b pb-3">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <ChartPie className="h-5 w-5 text-primary" />
                  <span>Subject Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-[300px] pt-6">
                <ThemeProvider theme={muiTheme}>
                  <PieChart
                    height={275}
                    series={[{
                      data: analytics?.subjectStats.map((subject, index) => ({
                        id: subject.subject,
                        value: secondsToHours(subject.totalDuration), // Convert to hours
                        label: subject.subject,
                        color: subjectColorMap[subject.subject] || getSubjectColor(index)
                      })) || [],
                      innerRadius: 30,
                      outerRadius: 100,
                      paddingAngle: 1,
                      cornerRadius: 5,
                      startAngle: -90,
                      endAngle: 270,
                      cx: 150,
                      cy: 150
                    }] as any}
                  />
                </ThemeProvider>
              </CardContent>
            </Card>

            <Card className="bg-card/60 backdrop-blur-sm border shadow-md rounded-xl overflow-hidden">
              <CardHeader className="border-b pb-3">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <BarChart2 className="h-5 w-5 text-primary" />
                  <span>Time Comparison</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-[300px] pt-6">
                <ThemeProvider theme={muiTheme}>
                  <BarChart
                    height={275}
                    xAxis={[{
                      scaleType: 'band',
                      data: analytics?.subjectStats
                        .sort((a, b) => b.totalDuration - a.totalDuration)
                        .slice(0, 5)
                        .map(s => s.subject) || [],
                      tickLabelStyle: {
                        angle: 0,
                        textAnchor: 'middle',
                      }
                    }]}
                    series={[
                      {
                        data: analytics?.subjectStats
                          .sort((a, b) => b.totalDuration - a.totalDuration)
                          .slice(0, 5)
                          .map(s => secondsToHours(s.totalDuration)) || [],
                        label: 'Hours',
                        valueFormatter: (value) => `${value.toFixed(1)}h`,
                        color: theme === 'dark' ? '#8b5cf6' : '#6366f1'
                      }
                    ]}
                  />
                </ThemeProvider>
              </CardContent>
            </Card>
          </div>

          {/* Subject Cards */}
          <div className="space-y-6">
            {sortedSubjects.map((subject, index) => (
              <div key={subject.subject} className="bg-card/60 backdrop-blur-sm border shadow-md rounded-xl overflow-hidden">
                <div className="flex flex-col lg:flex-row">
                  {/* Left Side - Subject Info */}
                  <div className="p-5 flex-1">
                    <div className="flex items-center gap-3 mb-4">
                      <div
                        className="w-12 h-12 rounded-xl flex items-center justify-center"
                        style={{ backgroundColor: `${subjectColorMap[subject.subject] || getSubjectColor(index)}20` }}
                      >
                        <div
                          className="w-6 h-6 rounded-md"
                          style={{ backgroundColor: subjectColorMap[subject.subject] || getSubjectColor(index) }}
                        />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{subject.subject}</h3>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3.5 w-3.5" />
                            {formatDuration(subject.totalDuration)}
                          </span>
                          <span>•</span>
                          <span className="flex items-center gap-1">
                            <Timer className="h-3.5 w-3.5" />
                            {subject.completedPomodoros} pomodoros
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-muted-foreground">Average Session</span>
                          <span className="text-sm font-medium">{formatDuration(subject.averageSessionDuration)}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div
                            className="h-full rounded-full"
                            style={{
                              width: `${Math.min(100, secondsToHours(subject.averageSessionDuration) * 100)}%`,
                              backgroundColor: subjectColorMap[subject.subject] || getSubjectColor(index)
                            }}
                          />
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-muted-foreground">Contribution to Total</span>
                          <span className="text-sm font-medium">
                            {
                              analytics.subjectStats.reduce((acc, s) => acc + s.totalDuration, 0) > 0
                                ? `${((subject.totalDuration / analytics.subjectStats.reduce((acc, s) => acc + s.totalDuration, 0)) * 100).toFixed(1)}%`
                                : '0%'
                            }
                          </span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div
                            className="h-full rounded-full"
                            style={{
                              width: `${
                                analytics.subjectStats.reduce((acc, s) => acc + s.totalDuration, 0) > 0
                                  ? ((subject.totalDuration / analytics.subjectStats.reduce((acc, s) => acc + s.totalDuration, 0)) * 100)
                                  : 0
                              }%`,
                              backgroundColor: subjectColorMap[subject.subject] || getSubjectColor(index)
                            }}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="p-3 bg-muted/30 rounded-lg border">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Pomodoro Time</span>
                            <span className="text-sm font-medium">
                              {formatDuration(subject.completedPomodoros * 25 * 60)}
                            </span>
                          </div>
                        </div>

                        <div className="p-3 bg-muted/30 rounded-lg border">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Time Rank</span>
                            <span className="text-sm font-medium">#{index + 1}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Side - Mini Chart */}
                  <div className="border-t lg:border-t-0 lg:border-l border-border p-5 w-full lg:w-72 flex-shrink-0">
                    <h4 className="text-sm font-medium mb-4">Recent Activity</h4>
                    <div className="h-40">
                      <ThemeProvider theme={muiTheme}>
                        {analytics.dailyStats.length > 0 ? (
                          <LineChart
                            height={150}
                            margin={{ top: 10, bottom: 20, left: 5, right: 5 }}
                            series={[
                              {
                                data: getRecentActivityData(subject.subject).data,
                                area: true,
                                showMark: false,
                                color: subjectColorMap[subject.subject] || getSubjectColor(index),
                              }
                            ]}
                            xAxis={[{
                              data: getRecentActivityData(subject.subject).labels,
                              scaleType: 'point',
                              tickLabelStyle: { fontSize: 10 }
                            }]}
                            axisHighlight={{x: 'none', y: 'none'}}
                            disableAxisListener
                          />
                        ) : (
                          <div className="h-full flex items-center justify-center">
                            <span className="text-sm text-muted-foreground">No activity data</span>
                          </div>
                        )}
                      </ThemeProvider>
                    </div>
                    <div className="mt-4 flex flex-col space-y-2">
                      <button 
                        className="w-full p-2 bg-muted/30 hover:bg-muted transition-colors rounded-lg border text-xs font-medium flex items-center justify-center gap-2"
                        onClick={() => handleViewDetailedStats(subject.subject)}
                      >
                        <FileBarChart className="h-3.5 w-3.5" />
                        View Detailed Stats
                      </button>
                      <button
                        className="w-full p-2 bg-primary/10 hover:bg-primary/20 text-primary transition-colors rounded-lg border border-primary/20 text-xs font-medium flex items-center justify-center gap-2"
                        style={{ color: subjectColorMap[subject.subject] || getSubjectColor(index) }}
                        onClick={() => handleStudySubject(subject.subject)}
                      >
                        <FolderOpenDot className="h-3.5 w-3.5" />
                        Study This Subject
                      </button>
                      <button
                        className="w-full p-2 bg-destructive hover:bg-destructive/90 text-destructive-foreground transition-colors rounded-lg border border-destructive/20 text-xs font-medium flex items-center justify-center gap-2"
                        onClick={() => {
                          const confirmDelete = window.confirm(`Are you sure you want to delete the subject "${subject.subject}"? This action cannot be undone.`);
                          if (confirmDelete) {
                            console.log(`Attempting to delete subject: ${subject.subject}`);
                            handleDeleteSubject(subject.subject);
                          }
                        }}
                        disabled={deletingSubject === subject.subject}
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                        {deletingSubject === subject.subject ? 'Deleting...' : 'Delete Subject'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Render the Subject Detail Modal */}
      {selectedSubjectData && analytics && (
        <SubjectDetailModal
          isOpen={isDetailModalOpen}
          onClose={() => setIsDetailModalOpen(false)}
          subjectData={selectedSubjectData}
          allSubjectStats={analytics.subjectStats || []}
          dailyStats={analytics.dailyStats || []}
          weeklyStats={analytics.weeklyStats || []}
          monthlyStats={analytics.monthlyStats || []}
          allSessions={allSessions || []} // Pass allSessions prop
          formatDuration={formatDuration}
          subjectColorMap={subjectColorMap}
          theme={theme}
          muiTheme={muiTheme}
        />
      )}
    </div>
  );
};

export default SubjectsTab;
