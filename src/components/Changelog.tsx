import { <PERSON><PERSON><PERSON><PERSON> } from "./ui/scroll-area";
import { Separator } from "./ui/separator";
import { <PERSON> } from "react-router-dom";
import { ChevronLeft, Clock, Star, Zap, Bug, Rocket, Paintbrush } from "lucide-react";
import { useEffect, useState } from "react";
import { Link as ScrollLink, Element, scroller } from 'react-scroll';

interface ChangelogEntry {
  version: string;
  date: string;
  sections: {
    title: string;
    changes: string[];
  }[];
}

const changelog: ChangelogEntry[] = [
  {
    version: "1.6.3",
    date: "March 8, 2025",
    sections: [
      {
        title: "UI Improvements",
        changes: [
          "Enhanced feedback button responsiveness: Optimized for better mobile experience",
          "Improved mobile layout: Compact design with simplified text on smaller screens",
          "Better touch targets: Adjusted button padding and icon sizes for mobile devices",
          "Consistent styling: Maintained visual harmony across all screen sizes"
        ]
      }
    ]
  },
  {
    version: "1.6.2",
    date: "March 7, 2025",
    sections: [
      {
        title: "UI Improvements",
        changes: [
          "Enhanced filter controls: Redesigned the filter UI in the table view for better usability",
          "Added visual filter badges: New badges show active filters with color-coding for different filter types",
          "Improved filter layout: Reorganized filters into a responsive grid with clear labels and icons",
          "Better filter feedback: Added clearer visual indicators for active filters and filter counts"
        ]
      }
    ]
  },
  {
    version: "1.6.1",
    date: "March 7, 2025",
    sections: [
      {
        title: "Fixes",
        changes: [
          "Fixed critical drag and drop issue: Resolved problem where dragging one task would move a different task",
          "Improved task identification: Now using draggable ID directly to ensure the correct task is moved",
          "Enhanced error handling: Added better validation and error reporting for drag and drop operations",
          "Improved type safety: Added proper type definitions for drag and drop operations"
        ]
      }
    ]
  },
  {
    version: "1.6.0",
    date: "March 7, 2025",
    sections: [
      {
        title: "Fixes",
        changes: [
          "Fixed drag and drop functionality: Resolved issues preventing tasks from being dragged and dropped",
          "Simplified drag and drop implementation: Removed overly complex validation that was causing errors",
          "Improved task card interaction: Made entire task card draggable for better user experience",
          "Enhanced reliability: Fixed edge cases in the drag and drop mechanism"
        ]
      }
    ]
  },
  {
    version: "1.5.9",
    date: "March 7, 2025",
    sections: [
      {
        title: "Fixes",
        changes: [
          "Fixed drag and drop mechanism: Resolved issue where dragging one task would move a different task",
          "Improved drag handle: Drag handle is now limited to the task title area for better control",
          "Enhanced error handling: Added validation and error logging for drag and drop operations",
          "Better visual feedback: Added opacity change during dragging for clearer user feedback"
        ]
      }
    ]
  },
  {
    version: "1.5.8",
    date: "March 7, 2025",
    sections: [
      {
        title: "Fixes",
        changes: [
          "Fixed overdue task detection: Tasks due on the current day are no longer marked as overdue",
          "Improved consistency: Updated all components to use the same overdue detection logic",
          "Enhanced user experience: More accurate overdue indicators across the application"
        ]
      }
    ]
  },
  {
    version: "1.5.7",
    date: "March 7, 2025",
    sections: [
      {
        title: "Features",
        changes: [
          "Enhanced filtering system: Added multiple filter options for priority, due date, and status",
          "Improved Kanban view: Added overdue task counters to each column for better visibility",
          "Better task organization: Overdue tasks are now highlighted and sorted to the top in both views",
          "Task count indicator: Added a counter showing how many tasks match the current filters"
        ]
      }
    ]
  },
  {
    version: "1.5.6",
    date: "March 9, 2024",
    sections: [
      {
        title: "UI Improvements",
        changes: [
          "Enhanced overdue task indicators with better contrast in both light and dark modes",
          "Added visual badges for overdue tasks with improved readability",
          "Refined the overdue task counter with a more prominent design",
          "Improved date display for overdue tasks with background highlighting"
        ]
      }
    ]
  },
  {
    version: "1.5.5",
    date: "March 7, 2025",
    sections: [
      {
        title: "Improvements",
        changes: [
          "Enhanced overdue tasks: Tasks with due dates on the current day are no longer marked as overdue",
          "Improved task sorting: Overdue tasks now appear at the top of lists, followed by tasks with earlier due dates",
          "Better visibility in dark mode: Adjusted colors for overdue task indicators to ensure visibility in both light and dark themes"
        ]
      }
    ]
  },
  {
    version: "1.5.4",
    date: "March 7, 2025",
    sections: [
      {
        title: "Features",
        changes: [
          "Added overdue tasks feature: Tasks past their due date are now highlighted in both Kanban and table views.",
          "New filter option: Users can now filter to show only overdue tasks in the table view.",
          "Overdue task counter: A counter in the board header shows how many tasks need attention.",
          "Visual indicators: Clear visual cues help identify overdue tasks at a glance."
        ]
      }
    ]
  },
  {
    version: "1.5.3",
    date: "March 7, 2025",
    sections: [
      {
        title: "Fixes",
        changes: [
          "Fixed priority sorting: Tasks are now correctly sorted by priority first, then by due date.",
          "Added description validation: Empty descriptions are no longer allowed when creating or editing tasks.",
          "Improved error handling: Clear error messages now guide users when required fields are missing."
        ]
      }
    ]
  },
  {
    version: "1.5.2",
    date: "March 7, 2025",
    sections: [
      {
        title: "Improvements",
        changes: [
          "Enhanced task prioritization: Tasks are now automatically sorted by due date and priority in both Kanban and table views.",
          "Improved task descriptions: Added detailed placeholders to guide users when creating or editing tasks.",
          "Better visual organization: Tasks with upcoming due dates and higher priorities now appear at the top of each column."
        ]
      }
    ]
  },
  {
    version: "1.5.1",
    date: "March 7, 2025",
    sections: [
      {
        title: "Features",
        changes: [
          "Added tabular view for tasks: Now you can switch between Kanban board and table view for your tasks.",
          "Enhanced task management: Edit task status directly from the table view.",
          "Improved task organization: Sort tasks by due date and priority in table view."
        ]
      }
    ]
  },
  {
    version: "1.5.0",
    date: "March 7, 2025",
    sections: [
      {
        title: "Features",
        changes: [
          "Added dedicated Tasks page with Todo + Kanban Board: Effortlessly manage your tasks and workflows with drag-and-drop functionality.",
          "Implemented real-time sync with Firestore for all tasks and columns.",
          "Added customizable columns with default Todo, In Progress, and Done columns.",
          "Integrated with Pomodoro timer for enhanced productivity.",
          "Added task creation with title, description, priority, and due date fields."
        ]
      }
    ]
  },
  {
    version: "1.3.6",
    date: "Feb 23, 2025",
    sections: [
      {
        title: "New Features",
        changes: [
          "Added user activity stats tracking with questions asked count",
          "Implemented daily streak system to track user engagement",
          "Added stats tab in settings with visual progress indicators",
          "Automatic streak updates based on daily activity"
        ]
      },
      {
        title: "UI Improvements",
        changes: [
          "Enhanced settings panel with tabbed interface",
          "Added elegant stats cards with progress visualization",
          "Improved profile management layout"
        ]
      },
      {
        title: "Performance",
        changes: [
          "Optimized stats tracking and storage in Firebase",
          "Improved real-time stats updates"
        ]
      }
    ]
  },
  {
    version: "1.3.5",
    date: "Feb 22, 2025",
    sections: [
      {
        title: "New Features",
        changes: [
          "Introducing Desmos graphing - now you can visualize mathematical functions by using the `/plot` command in chat",
          "Enhanced periodic table with a cleaner, more intuitive layout for better chemistry learning",
          "New interactive tools to help you understand complex concepts better"
        ]
      },
      {
        title: "UI Improvements",
        changes: [
          "Smoother and more responsive chat experience",
          "Better visualization of mathematical and scientific content",
          "Cleaner and more organized interface for better learning"
        ]
      }
    ]
  },
  {
    version: "1.3.4",
    date: "Feb 22, 2025",
    sections: [
      {
        title: "New Features",
        changes: [
          "Integrated Desmos graphing tool with `/plot` command",
          "Added new `Tools` component for enhanced tool interactions",
          "Added Three.js and Tween.js support for improved visualizations"
        ]
      },
      {
        title: "UI Improvements",
        changes: [
          "Refactored periodic table component with optimized styling",
          "Standardized element cell width to 60px for better consistency",
          "Enhanced chat interface to support tool output rendering"
        ]
      },
      {
        title: "Performance",
        changes: [
          "Simplified element cell rendering logic",
          "Improved error handling and tool command processing",
          "Optimized component layout and styling structure"
        ]
      }
    ]
  },
  {
    version: "1.3.3",
    date: "Feb 21, 2025",
    sections: [
      {
        title: "UI Improvements",
        changes: [
          "Enhanced scroll indicator with larger, more visible mouse icon",
          "Added glowing effect and hover animations to scroll indicator",
          "Improved smooth scrolling behavior with better timing",
          "Implemented slower, more elegant chevron animation"
        ]
      }
    ]
  },
  {
    version: "1.3.2",
    date: "Feb 20, 2025",
    sections: [
      {
        title: "New Features",
        changes: [
          "Added discussion feature with comments and replies",
          "Implemented user authentication and profile system",
          "Added protected routes for authenticated users",
          "Integrated Google Analytics for better user insights"
        ]
      },
      {
        title: "UI Improvements",
        changes: [
          "Added settings panel with user profile management",
          "Implemented chat history management with star and delete functionality",
          "Enhanced PWA experience with better update notifications",
          "Added paste image support in chat interface"
        ]
      },
      {
        title: "Performance",
        changes: [
          "Improved cache management for better app performance",
          "Enhanced PWA caching strategy",
          "Optimized chat history storage and retrieval"
        ]
      }
    ]
  },
  {
    version: "1.3.1",
    date: "Feb 17, 2025",
    sections: [
      {
        title: "UI Improvements",
        changes: [
          "Enhanced landing page with elegant animations and shapes",
          "Added smooth scroll indicator with mouse icon",
          "Improved button design with gradient animation",
          "Added features section with modern card layout",
          "Enhanced overall visual hierarchy and typography"
        ]
      }
    ]
  },
  {
    version: "1.3.0",
    date: "Feb 16, 2025",
    sections: [
      {
        title: "New Features",
        changes: [
          "Added AI page with integrated chat functionality",
          "Implemented local storage for chat persistence",
          "Enhanced LaTeX formatting and rendering"
        ]
      },
      {
        title: "Infrastructure",
        changes: [
          "Updated sitemap.xml with new pages and timestamps"
        ]
      }
    ]
  },
  {
    version: "1.2.1",
    date: "Feb 11, 2025",
    sections: [
      {
        title: "UI Improvements",
        changes: [
          "Implemented fullscreen chat interface",
          "Added header to fullscreen chat mode",
          "Enhanced dark mode theme",
          "Improved chat window layout and responsiveness"
        ]
      },
      {
        title: "Bug Fixes",
        changes: [
          "Fixed fullscreen chat functionality issues",
          "Resolved useToast import issues in ChatInterface"
        ]
      }
    ]
  },
  {
    version: "1.2.0",
    date: "Jan 24-26, 2025",
    sections: [
      {
        title: "New Features",
        changes: [
          "Integrated JSXGraph for mathematical diagrams",
          "Added expandable chat window functionality",
          "Implemented chat continuation feature",
          "Added developer mode"
        ]
      },
      {
        title: "UI Improvements",
        changes: [
          "Enhanced chatbox interface",
          "Added automatic scroll to bottom in ChatInterface",
          "Implemented 404 error page"
        ]
      },
      {
        title: "Bug Fixes",
        changes: [
          "Fixed build errors related to JSXGraph",
          "Resolved TypeError in mapping function",
          "Fixed package installation issues",
          "Corrected type error in ChatInterface props"
        ]
      },
      {
        title: "Performance",
        changes: [
          "Implemented cache control optimizations",
          "Enhanced build system stability"
        ]
      }
    ]
  },
  {
    version: "1.1.0",
    date: "Jan 30, 2024",
    sections: [
      {
        title: "New Features",
        changes: [
          "Implemented dark mode support",
          "Added PWA functionality for app installation",
          "Enhanced image upload and processing capabilities",
          "Added offline mode support",
          "Implemented user name customization"
        ]
      },
      {
        title: "UI Improvements",
        changes: [
          "Added loading states and animations",
          "Enhanced mobile responsive design",
          "Improved chat interface layout"
        ]
      },
      {
        title: "Bug Fixes",
        changes: [
          "Fixed image cropping issues",
          "Resolved chat history persistence issues",
          "Fixed mobile navigation bugs"
        ]
      }
    ]
  },
  {
    version: "1.0.0",
    date: "Jan 15, 2024",
    sections: [
      {
        title: "Initial Release",
        changes: [
          "Core chat interface implementation",
          "Integration with Gemini AI for PCM doubt solving",
          "Basic image upload functionality",
          "Responsive design foundation",
          "Subject-specific expertise system for Physics, Chemistry, and Mathematics"
        ]
      },
      {
        title: "Features",
        changes: [
          "Real-time chat interactions",
          "Step-by-step problem solving",
          "Basic error handling",
          "Mobile-friendly interface"
        ]
      }
    ]
  }
];

const Badge = ({ children, variant }: { children: React.ReactNode; variant: 'new' | 'improvement' | 'fix' | 'performance' }) => {
  const variants = {
    new: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100',
    improvement: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100',
    fix: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100',
    performance: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]}`}>
      {children}
    </span>
  );
};

const SectionIcon = ({ title }: { title: string }) => {
  const icons = {
    "New Features": <Rocket className="w-5 h-5 text-green-500" />,
    "UI Improvements": <Paintbrush className="w-5 h-5 text-blue-500" />,
    "Bug Fixes": <Bug className="w-5 h-5 text-red-500" />,
    "Performance": <Zap className="w-5 h-5 text-purple-500" />,
    "Infrastructure": <Star className="w-5 h-5 text-yellow-500" />
  };

  return icons[title as keyof typeof icons] || null;
};

const TableOfContents = ({ entries }: { entries: ChangelogEntry[] }) => {
  const [activeVersion, setActiveVersion] = useState<string>("");

  useEffect(() => {
    const observers = entries.map(entry => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setActiveVersion(entry.target.id.slice(1)); // Remove 'v' prefix
          }
        },
        {
          rootMargin: "-80px 0px -80% 0px"
        }
      );

      const element = document.getElementById(`v${entry.version}`);
      if (element) {
        observer.observe(element);
      }

      return observer;
    });

    return () => {
      observers.forEach(observer => observer.disconnect());
    };
  }, [entries]);

  const scrollToVersion = (version: string) => {
    scroller.scrollTo(`v${version}`, {
      duration: 800,
      delay: 0,
      smooth: 'easeInOutQuart',
      offset: -80
    });
  };

  return (
    <div className="sticky top-4 p-6 bg-card rounded-xl border shadow-sm">
      <h2 className="text-lg font-semibold mb-4 font-spaceGrotesk-variable flex items-center gap-2">
        <Clock className="w-5 h-5" />
        Version History
      </h2>
      <ul className="space-y-2">
        {entries.map((entry) => (
          <li key={entry.version}>
            <button
              onClick={() => scrollToVersion(entry.version)}
              className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                activeVersion === entry.version
                  ? "bg-primary/10 text-primary"
                  : "hover:bg-primary/5"
              }`}
            >
              v{entry.version}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export function Changelog() {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-[#030303]">
      <div className="max-w-6xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-[1fr,320px] gap-12">
          <div>
            <Link 
              to="/" 
              className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6 transition-colors"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Back to main
            </Link>
            <h1 className="text-5xl font-bold tracking-tight font-spaceGrotesk-variable mb-4">Changelog</h1>
            <p className="text-muted-foreground text-lg mb-8">
              Track the evolution of IsotopeAI through our latest updates and improvements.
            </p>
            <Separator className="my-8" />

            <div className="space-y-16 pb-8">
              {changelog.map((entry, index) => (
                <Element name={`v${entry.version}`} key={entry.version} id={`v${entry.version}`} className="scroll-mt-20">
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <h2 className="text-3xl font-bold tracking-tight font-spaceGrotesk-variable">
                          v{entry.version}
                        </h2>
                        {index === 0 && <Badge variant="new">Latest</Badge>}
                      </div>
                      <p className="text-muted-foreground">{entry.date}</p>
                    </div>

                    {entry.sections.map((section, sectionIndex) => (
                      <div key={sectionIndex} className="space-y-4">
                        <div className="flex items-center gap-2">
                          <SectionIcon title={section.title} />
                          <h3 className="text-xl font-semibold font-spaceGrotesk-variable">{section.title}</h3>
                        </div>
                        <ul className="space-y-3 pl-7">
                          {section.changes.map((change, changeIndex) => (
                            <li key={changeIndex} className="text-muted-foreground relative">
                              <span className="absolute -left-7 top-2 w-2 h-2 rounded-full bg-muted-foreground"></span>
                              {change}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}

                    {index !== changelog.length - 1 && <Separator className="my-12" />}
                  </div>
                </Element>
              ))}
            </div>
          </div>

          <div className="hidden lg:block">
            <TableOfContents entries={changelog} />
          </div>
        </div>
      </div>
    </div>
  );
}