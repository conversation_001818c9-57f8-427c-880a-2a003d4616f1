import React, { useEffect, useState } from 'react';
import { Button } from './ui/button';
import { X } from 'lucide-react';

export const PWAPrompt = () => {
  const [showIOSPrompt, setShowIOSPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    // Check if already installed
    const isInstalled = window.matchMedia('(display-mode: standalone)').matches;
    if (isInstalled) return;

    // Delay prompt to not interrupt initial user experience
    const promptDelay = setTimeout(() => {
      // Check if it's iOS
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      if (isIOS && isSafari) {
        // Check last dismissal time for iOS prompt
        const iosPromptDismissed = localStorage.getItem('pwa-ios-prompt-dismissed');
        const lastDismissedTime = iosPromptDismissed ? parseInt(iosPromptDismissed) : 0;
        const daysSinceLastDismissal = (Date.now() - lastDismissedTime) / (1000 * 60 * 60 * 24);
        
        // Only show if never dismissed or dismissed more than 30 days ago
        if (!iosPromptDismissed || daysSinceLastDismissal > 30) {
          setShowIOSPrompt(true);
        }
      }

      // Handle Chrome/Edge PWA install prompt
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        setDeferredPrompt(e);
        
        // Check last dismissal time for regular prompt
        const promptDismissed = localStorage.getItem('pwa-prompt-dismissed');
        const lastDismissedTime = promptDismissed ? parseInt(promptDismissed) : 0;
        const daysSinceLastDismissal = (Date.now() - lastDismissedTime) / (1000 * 60 * 60 * 24);
        
        // Only show if never dismissed or dismissed more than 14 days ago
        if (!promptDismissed || daysSinceLastDismissal > 14) {
          setShowPrompt(true);
        }
      });
    }, 3000); // Show after 3 seconds

    return () => clearTimeout(promptDelay);
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) return;
    
    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      setShowPrompt(false);
    }
    setDeferredPrompt(null);
  };

  const dismissPrompt = (type: 'ios' | 'other') => {
    if (type === 'ios') {
      setShowIOSPrompt(false);
      localStorage.setItem('pwa-ios-prompt-dismissed', Date.now().toString());
    } else {
      setShowPrompt(false);
      localStorage.setItem('pwa-prompt-dismissed', Date.now().toString());
    }
  };

  if (!showPrompt && !showIOSPrompt) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 p-4 bg-card shadow-lg border-t border-border z-50 animate-in slide-in-from-bottom duration-300">
      {showIOSPrompt ? (
        <div className="flex items-center justify-between gap-4">
          <div className="flex-1">
            <p className="text-sm">
              Install this app on your iPhone: tap <span className="inline-block">
                <svg className="w-4 h-4 inline" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 5l-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4 4 4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2z" />
                </svg>
              </span> and then "Add to Home Screen"
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => dismissPrompt('ios')}
            className="shrink-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between gap-4">
          <div className="flex-1">
            <p className="text-sm font-medium">Install IsotopeAI for better experience</p>
            <p className="text-xs text-muted-foreground mt-1">Access the app offline and get a faster experience</p>
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={handleInstall}
              className="text-xs"
            >
              Install
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => dismissPrompt('other')}
              className="shrink-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}; 