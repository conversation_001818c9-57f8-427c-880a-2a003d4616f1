import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Target, TrendingUp, Medal, Layers } from "lucide-react";
import { MockTestAnalytics } from "@/types/mockTest";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface MockTestAnalyticsCardsProps {
  analytics: MockTestAnalytics;
}

export function MockTestAnalyticsCards({ analytics }: MockTestAnalyticsCardsProps) {
  // Function to determine color based on score
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500";
    if (score >= 75) return "text-green-400";
    if (score >= 60) return "text-yellow-500";
    if (score >= 40) return "text-orange-500";
    return "text-red-500";
  };

  // Function to determine background gradient based on score
  const getScoreGradient = (score: number) => {
    if (score >= 90) return "from-green-500/20 to-green-500/5";
    if (score >= 75) return "from-green-400/20 to-green-400/5";
    if (score >= 60) return "from-yellow-500/20 to-yellow-500/5";
    if (score >= 40) return "from-orange-500/20 to-orange-500/5";
    return "from-red-500/20 to-red-500/5";
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <Card className="overflow-hidden border border-primary/10 shadow-md hover:shadow-lg transition-all duration-300 group">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <BookOpen className="h-4 w-4 text-primary" />
            Total Tests
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
            <Layers className="h-4 w-4 text-primary" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{analytics.totalTests}</div>
          <p className="text-sm text-muted-foreground mt-1">
            Tests recorded in your history
          </p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border border-primary/10 shadow-md hover:shadow-lg transition-all duration-300 group relative">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <PieChart className="h-4 w-4 text-primary" />
            Average Score
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
            <TrendingUp className="h-4 w-4 text-primary" />
          </div>
        </CardHeader>
        <CardContent>
          <div className={cn("text-3xl font-bold", getScoreColor(analytics.averageScore))}>
            {analytics.averageScore.toFixed(2)}%
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Average across all your tests
          </p>
          <div className="mt-2 h-1.5 w-full bg-muted rounded-full overflow-hidden">
            <div
              className={cn("h-full rounded-full bg-gradient-to-r", getScoreGradient(analytics.averageScore))}
              style={{ width: `${Math.min(100, analytics.averageScore)}%` }}
            ></div>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border border-primary/10 shadow-md hover:shadow-lg transition-all duration-300 group relative">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Target className="h-4 w-4 text-primary" />
            Highest Score
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
            <Medal className="h-4 w-4 text-primary" />
          </div>
        </CardHeader>
        <CardContent className="relative">
          <div className={cn("text-3xl font-bold", getScoreColor(analytics.highestScore.percentage))}>
            {analytics.highestScore.percentage.toFixed(2)}%
          </div>
          <p className="text-sm text-muted-foreground mt-1 line-clamp-1">
            {analytics.highestScore.testName || 'No tests recorded'}
          </p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border border-primary/10 shadow-md hover:shadow-lg transition-all duration-300 group relative">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-primary" />
            Subjects Tested
          </CardTitle>
          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
            <BookOpen className="h-4 w-4 text-primary" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">
            {Object.keys(analytics.subjectPerformance).length}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            {Object.keys(analytics.subjectPerformance).length > 0
              ? (
                <span className="line-clamp-1">
                  {Object.keys(analytics.subjectPerformance).join(', ')}
                </span>
              )
              : 'No subjects tested yet'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}