import React, { useState, useRef, useEffect } from 'react';
import React<PERSON><PERSON>, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { Button } from './ui/button';
import { Send, X } from 'lucide-react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from './ui/dialog';

interface ImageEditorProps {
  imageFile: File;
  onSend: (editedImage: File) => void;
  onCancel: () => void;
  open: boolean;
}

export const ImageEditor = ({ imageFile, onSend, onCancel, open }: ImageEditorProps) => {
  const [crop, setCrop] = useState<Crop>();
  const [imageUrl, setImageUrl] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);

  useEffect(() => {
    if (imageFile) {
      try {
        const url = URL.createObjectURL(imageFile);
        console.log('Created image URL:', url);
        setImageUrl(url);
        setError(null);
        return () => {
          console.log('Cleaning up image URL:', url);
          URL.revokeObjectURL(url);
        };
      } catch (err) {
        console.error('Error creating image URL:', err);
        setError('Failed to load image preview');
      }
    }
  }, [imageFile]);

  const handleComplete = async () => {
    if (!imageRef.current || !crop) {
      // Validate original file isn't empty before sending
      if (imageFile.size === 0) {
        console.error('Cannot use empty image file');
        return;
      }
      onSend(imageFile); // If no crop, send original
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Get the natural dimensions of the image
    const naturalWidth = imageRef.current.naturalWidth;
    const naturalHeight = imageRef.current.naturalHeight;

    // Calculate the scale factors
    const scaleX = naturalWidth / imageRef.current.width;
    const scaleY = naturalHeight / imageRef.current.height;

    // Set canvas dimensions to match the cropped area's natural size
    canvas.width = crop.width * scaleX;
    canvas.height = crop.height * scaleY;

    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    ctx.drawImage(
      imageRef.current,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      canvas.width,
      canvas.height
    );

    canvas.toBlob(
      (blob) => {
        if (blob) {
          if (blob.size === 0) {
            console.error('Generated empty blob after cropping');
            // Fall back to original file if cropped image is empty
            if (imageFile.size > 0) {
              onSend(imageFile);
            }
            return;
          }
          const file = new File([blob], imageFile.name, { type: 'image/jpeg' });
          onSend(file);
        } else {
          console.error('Failed to generate blob from canvas');
          // Fall back to original file if blob generation fails
          if (imageFile.size > 0) {
            onSend(imageFile);
          }
        }
      },
      'image/jpeg',
      1.0 // Maximum quality
    );
  };

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crop Image</DialogTitle>
          <DialogDescription>
            Adjust the crop area before sending
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          <div className="relative flex items-center justify-center bg-secondary/50 rounded-lg p-4">
            {error ? (
              <div className="text-destructive">{error}</div>
            ) : (
              <ReactCrop
                crop={crop}
                onChange={c => setCrop(c)}
                aspect={undefined}
              >
                <img
                  ref={imageRef}
                  src={imageUrl}
                  alt="Upload preview"
                  className="max-w-full h-auto object-contain"
                  style={{ maxHeight: 'calc(70vh - 200px)' }}
                  onError={(e) => {
                    console.error('Error loading image:', e);
                    setError('Failed to display image');
                  }}
                />
              </ReactCrop>
            )}
          </div>

          <div className="flex justify-end gap-2 sticky bottom-0 bg-background p-2">
            <Button variant="outline" onClick={onCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleComplete}>
              <Send className="w-4 h-4 mr-2" />
              Send
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};