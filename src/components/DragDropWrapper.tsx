import React from 'react';
import { useToast } from "@/hooks/use-toast";

interface DragDropWrapperProps {
  children: React.ReactNode;
  onImageDrop: (file: File) => void;
  className?: string;
  acceptedFileTypes?: string[];
  errorMessage?: string;
}

export const DragDropWrapper = ({ 
  children, 
  onImageDrop, 
  className,
  acceptedFileTypes = ['image/*'],
  errorMessage = "Invalid file type. Please drop an image."
}: DragDropWrapperProps) => {
  const { toast } = useToast();

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.style.opacity = "0.7";
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.style.opacity = "1";
  };

  const isFileTypeAccepted = (file: File) => {
    return acceptedFileTypes.some(type => {
      if (type === 'image/*') {
        return file.type.startsWith('image/');
      }
      return file.type === type;
    });
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.style.opacity = "1";
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      
      // Validate file size to prevent empty files
      if (file.size === 0) {
        toast({
          title: "Error",
          description: "The image file is empty or invalid. Please try with a different image.",
          variant: "destructive",
        });
        return;
      }
      
      if (isFileTypeAccepted(file)) {
        onImageDrop(file);
      } else {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    }
  };

  return (
    <div
      className={className}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {children}
    </div>
  );
};