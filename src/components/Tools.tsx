import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from "./ui/dialog";
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "./ui/tabs";
import { <PERSON><PERSON> } from "./ui/button";
import { Calculator, ChevronRight, LineChart, Ruler, Atom, Plus, Minus, X, Divide, Equal, RefreshCw, Percent, Sparkles } from "lucide-react";
import { DesmosGraph } from './DesmosGraph';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Separator } from "./ui/separator";

interface ToolsProps {
  onToolOutput: (output: any) => void;
}

export const Tools = ({ onToolOutput }: ToolsProps) => {
  const [expression, setExpression] = useState('y=x^2');
  const [open, setOpen] = useState(false);
  const [calculatorDisplay, setCalculatorDisplay] = useState('0');
  const [calculatorMemory, setCalculatorMemory] = useState<number | null>(null);
  const [calculatorOperation, setCalculatorOperation] = useState<string | null>(null);
  const [convertValue, setConvertValue] = useState('');
  const [fromUnit, setFromUnit] = useState('m');
  const [toUnit, setToUnit] = useState('km');
  const [isScientific, setIsScientific] = useState(false);
  const [convertResult, setConvertResult] = useState<string | null>(null);
  const [elementDetails, setElementDetails] = useState<{
    symbol: string;
    name: string;
    atomicNumber: number;
    atomicMass: number;
    category: string;
  } | null>(null);
  const [elementDialogOpen, setElementDialogOpen] = useState(false);

  // Calculator functions
  const handleNumber = (num: string) => {
    setCalculatorDisplay(prev => prev === '0' ? num : prev + num);
  };

  const handleOperation = (op: string) => {
    setCalculatorMemory(parseFloat(calculatorDisplay));
    setCalculatorOperation(op);
    setCalculatorDisplay('0');
  };

  const handleEqual = () => {
    if (calculatorMemory === null || calculatorOperation === null) return;
    
    const current = parseFloat(calculatorDisplay);
    let result = 0;
    
    switch (calculatorOperation) {
      case '+':
        result = calculatorMemory + current;
        break;
      case '-':
        result = calculatorMemory - current;
        break;
      case '*':
        result = calculatorMemory * current;
        break;
      case '/':
        result = calculatorMemory / current;
        break;
      case 'pow':
        result = Math.pow(calculatorMemory, current);
        break;
    }
    
    setCalculatorDisplay(result.toString());
    setCalculatorMemory(null);
    setCalculatorOperation(null);
  };

  const handleClear = () => {
    setCalculatorDisplay('0');
    setCalculatorMemory(null);
    setCalculatorOperation(null);
  };

  const handleScientificOperation = (operation: string) => {
    const value = parseFloat(calculatorDisplay);
    let result = 0;

    switch (operation) {
      case 'square':
        result = value * value;
        break;
      case 'sqrt':
        result = Math.sqrt(value);
        break;
      case 'sin':
        result = Math.sin(value * Math.PI / 180);
        break;
      case 'cos':
        result = Math.cos(value * Math.PI / 180);
        break;
      case 'tan':
        result = Math.tan(value * Math.PI / 180);
        break;
      case 'log':
        result = Math.log10(value);
        break;
      case 'ln':
        result = Math.log(value);
        break;
      case 'percent':
        result = value / 100;
        break;
      case 'pi':
        result = Math.PI;
        break;
      case 'e':
        result = Math.E;
        break;
    }

    setCalculatorDisplay(result.toString());
  };

  // Unit conversion functions
  const handleConvert = () => {
    if (!convertValue) return;
    
    const value = parseFloat(convertValue);
    let result = 0;
    
    const conversions: Record<string, Record<string, number>> = {
      'm': {
        'km': 0.001,
        'cm': 100,
        'mm': 1000,
        'm': 1
      },
      'km': {
        'm': 1000,
        'cm': 100000,
        'mm': 1000000,
        'km': 1
      },
      'cm': {
        'm': 0.01,
        'km': 0.00001,
        'mm': 10,
        'cm': 1
      },
      'mm': {
        'm': 0.001,
        'km': 0.000001,
        'cm': 0.1,
        'mm': 1
      }
    };

    if (conversions[fromUnit] && conversions[fromUnit][toUnit]) {
      result = value * conversions[fromUnit][toUnit];
      setConvertResult(`${value} ${fromUnit} = ${result} ${toUnit}`);
    }
  };

  // Periodic table rendering
  const renderPeriodicTable = () => {
    const atomicMasses = {
      'H': 1.008, 'He': 4.003, 'Li': 6.941, 'Be': 9.012, 'B': 10.811, 'C': 12.011, 'N': 14.007, 'O': 15.999,
      'F': 18.998, 'Ne': 20.180, 'Na': 22.990, 'Mg': 24.305, 'Al': 26.982, 'Si': 28.086, 'P': 30.974, 'S': 32.065,
      'Cl': 35.453, 'Ar': 39.948, 'K': 39.098, 'Ca': 40.078, 'Sc': 44.956, 'Ti': 47.867, 'V': 50.942, 'Cr': 51.996,
      'Mn': 54.938, 'Fe': 55.845, 'Co': 58.933, 'Ni': 58.693, 'Cu': 63.546, 'Zn': 65.380, 'Ga': 69.723, 'Ge': 72.640,
      'As': 74.922, 'Se': 78.960, 'Br': 79.904, 'Kr': 83.798, 'Rb': 85.468, 'Sr': 87.620, 'Y': 88.906, 'Zr': 91.224,
      'Nb': 92.906, 'Mo': 95.960, 'Tc': 98.000, 'Ru': 101.070, 'Rh': 102.906, 'Pd': 106.420, 'Ag': 107.868, 'Cd': 112.411,
      'In': 114.818, 'Sn': 118.710, 'Sb': 121.760, 'Te': 127.600, 'I': 126.904, 'Xe': 131.293, 'Cs': 132.905, 'Ba': 137.327,
      'La': 138.905, 'Ce': 140.116, 'Pr': 140.908, 'Nd': 144.242, 'Pm': 145.000, 'Sm': 150.360, 'Eu': 151.964, 'Gd': 157.250,
      'Tb': 158.925, 'Dy': 162.500, 'Ho': 164.930, 'Er': 167.259, 'Tm': 168.934, 'Yb': 173.054, 'Lu': 174.967, 'Hf': 178.490,
      'Ta': 180.948, 'W': 183.840, 'Re': 186.207, 'Os': 190.230, 'Ir': 192.217, 'Pt': 195.084, 'Au': 196.967, 'Hg': 200.590,
      'Tl': 204.383, 'Pb': 207.200, 'Bi': 208.980, 'Po': 209.000, 'At': 210.000, 'Rn': 222.000, 'Fr': 223.000, 'Ra': 226.000,
      'Ac': 227.000, 'Th': 232.038, 'Pa': 231.036, 'U': 238.029, 'Np': 237.048, 'Pu': 244.064, 'Am': 243.061, 'Cm': 247.070,
      'Bk': 247.070, 'Cf': 251.080, 'Es': 252.083, 'Fm': 257.095, 'Md': 258.098, 'No': 259.101, 'Lr': 262.110,
      'Rf': 267.122, 'Db': 268.126, 'Sg': 269.128, 'Bh': 270.133, 'Hs': 269.134, 'Mt': 278.156, 'Ds': 281.165,
      'Rg': 282.169, 'Cn': 285.177, 'Nh': 286.182, 'Fl': 289.190, 'Mc': 290.196, 'Lv': 293.205, 'Ts': 294.211, 'Og': 294.214
    };
  
    const elements = [
      // Period 1
      [{ symbol: 'H', name: 'Hydrogen', category: 'nonmetal', atomicNumber: 1 }, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, { symbol: 'He', name: 'Helium', category: 'noble-gas', atomicNumber: 2 }],
      // Period 2
      [{ symbol: 'Li', name: 'Lithium', category: 'alkali-metal', atomicNumber: 3 }, { symbol: 'Be', name: 'Beryllium', category: 'alkaline-earth', atomicNumber: 4 }, null, null, null, null, null, null, null, null, null, null, { symbol: 'B', name: 'Boron', category: 'metalloid', atomicNumber: 5 }, { symbol: 'C', name: 'Carbon', category: 'nonmetal', atomicNumber: 6 }, { symbol: 'N', name: 'Nitrogen', category: 'nonmetal', atomicNumber: 7 }, { symbol: 'O', name: 'Oxygen', category: 'nonmetal', atomicNumber: 8 }, { symbol: 'F', name: 'Fluorine', category: 'halogen', atomicNumber: 9 }, { symbol: 'Ne', name: 'Neon', category: 'noble-gas', atomicNumber: 10 }],
      // Period 3
      [{ symbol: 'Na', name: 'Sodium', category: 'alkali-metal', atomicNumber: 11 }, { symbol: 'Mg', name: 'Magnesium', category: 'alkaline-earth', atomicNumber: 12 }, null, null, null, null, null, null, null, null, null, null, { symbol: 'Al', name: 'Aluminum', category: 'post-transition', atomicNumber: 13 }, { symbol: 'Si', name: 'Silicon', category: 'metalloid', atomicNumber: 14 }, { symbol: 'P', name: 'Phosphorus', category: 'nonmetal', atomicNumber: 15 }, { symbol: 'S', name: 'Sulfur', category: 'nonmetal', atomicNumber: 16 }, { symbol: 'Cl', name: 'Chlorine', category: 'halogen', atomicNumber: 17 }, { symbol: 'Ar', name: 'Argon', category: 'noble-gas', atomicNumber: 18 }],
      // Period 4
      [{ symbol: 'K', name: 'Potassium', category: 'alkali-metal', atomicNumber: 19 }, { symbol: 'Ca', name: 'Calcium', category: 'alkaline-earth', atomicNumber: 20 }, { symbol: 'Sc', name: 'Scandium', category: 'transition', atomicNumber: 21 }, { symbol: 'Ti', name: 'Titanium', category: 'transition', atomicNumber: 22 }, { symbol: 'V', name: 'Vanadium', category: 'transition', atomicNumber: 23 }, { symbol: 'Cr', name: 'Chromium', category: 'transition', atomicNumber: 24 }, { symbol: 'Mn', name: 'Manganese', category: 'transition', atomicNumber: 25 }, { symbol: 'Fe', name: 'Iron', category: 'transition', atomicNumber: 26 }, { symbol: 'Co', name: 'Cobalt', category: 'transition', atomicNumber: 27 }, { symbol: 'Ni', name: 'Nickel', category: 'transition', atomicNumber: 28 }, { symbol: 'Cu', name: 'Copper', category: 'transition', atomicNumber: 29 }, { symbol: 'Zn', name: 'Zinc', category: 'post-transition', atomicNumber: 30 }, { symbol: 'Ga', name: 'Gallium', category: 'post-transition', atomicNumber: 31 }, { symbol: 'Ge', name: 'Germanium', category: 'metalloid', atomicNumber: 32 }, { symbol: 'As', name: 'Arsenic', category: 'metalloid', atomicNumber: 33 }, { symbol: 'Se', name: 'Selenium', category: 'nonmetal', atomicNumber: 34 }, { symbol: 'Br', name: 'Bromine', category: 'halogen', atomicNumber: 35 }, { symbol: 'Kr', name: 'Krypton', category: 'noble-gas', atomicNumber: 36 }],
      // Period 5
      [{ symbol: 'Rb', name: 'Rubidium', category: 'alkali-metal', atomicNumber: 37 }, { symbol: 'Sr', name: 'Strontium', category: 'alkaline-earth', atomicNumber: 38 }, { symbol: 'Y', name: 'Yttrium', category: 'transition', atomicNumber: 39 }, { symbol: 'Zr', name: 'Zirconium', category: 'transition', atomicNumber: 40 }, { symbol: 'Nb', name: 'Niobium', category: 'transition', atomicNumber: 41 }, { symbol: 'Mo', name: 'Molybdenum', category: 'transition', atomicNumber: 42 }, { symbol: 'Tc', name: 'Technetium', category: 'transition', atomicNumber: 43 }, { symbol: 'Ru', name: 'Ruthenium', category: 'transition', atomicNumber: 44 }, { symbol: 'Rh', name: 'Rhodium', category: 'transition', atomicNumber: 45 }, { symbol: 'Pd', name: 'Palladium', category: 'transition', atomicNumber: 46 }, { symbol: 'Ag', name: 'Silver', category: 'transition', atomicNumber: 47 }, { symbol: 'Cd', name: 'Cadmium', category: 'post-transition', atomicNumber: 48 }, { symbol: 'In', name: 'Indium', category: 'post-transition', atomicNumber: 49 }, { symbol: 'Sn', name: 'Tin', category: 'post-transition', atomicNumber: 50 }, { symbol: 'Sb', name: 'Antimony', category: 'metalloid', atomicNumber: 51 }, { symbol: 'Te', name: 'Tellurium', category: 'metalloid', atomicNumber: 52 }, { symbol: 'I', name: 'Iodine', category: 'halogen', atomicNumber: 53 }, { symbol: 'Xe', name: 'Xenon', category: 'noble-gas', atomicNumber: 54 }],
      // Period 6
      [{ symbol: 'Cs', name: 'Cesium', category: 'alkali-metal', atomicNumber: 55 }, { symbol: 'Ba', name: 'Barium', category: 'alkaline-earth', atomicNumber: 56 }, { symbol: '*', name: 'Lanthanides', category: 'lanthanide-label', atomicNumber: '57-71' }, { symbol: 'Hf', name: 'Hafnium', category: 'transition', atomicNumber: 72 }, { symbol: 'Ta', name: 'Tantalum', category: 'transition', atomicNumber: 73 }, { symbol: 'W', name: 'Tungsten', category: 'transition', atomicNumber: 74 }, { symbol: 'Re', name: 'Rhenium', category: 'transition', atomicNumber: 75 }, { symbol: 'Os', name: 'Osmium', category: 'transition', atomicNumber: 76 }, { symbol: 'Ir', name: 'Iridium', category: 'transition', atomicNumber: 77 }, { symbol: 'Pt', name: 'Platinum', category: 'transition', atomicNumber: 78 }, { symbol: 'Au', name: 'Gold', category: 'transition', atomicNumber: 79 }, { symbol: 'Hg', name: 'Mercury', category: 'post-transition', atomicNumber: 80 }, { symbol: 'Tl', name: 'Thallium', category: 'post-transition', atomicNumber: 81 }, { symbol: 'Pb', name: 'Lead', category: 'post-transition', atomicNumber: 82 }, { symbol: 'Bi', name: 'Bismuth', category: 'post-transition', atomicNumber: 83 }, { symbol: 'Po', name: 'Polonium', category: 'metalloid', atomicNumber: 84 }, { symbol: 'At', name: 'Astatine', category: 'halogen', atomicNumber: 85 }, { symbol: 'Rn', name: 'Radon', category: 'noble-gas', atomicNumber: 86 }],
      // Period 7
      [{ symbol: 'Fr', name: 'Francium', category: 'alkali-metal', atomicNumber: 87 }, { symbol: 'Ra', name: 'Radium', category: 'alkaline-earth', atomicNumber: 88 }, { symbol: '**', name: 'Actinides', category: 'actinide-label', atomicNumber: '89-103' }, { symbol: 'Rf', name: 'Rutherfordium', category: 'transition', atomicNumber: 104 }, { symbol: 'Db', name: 'Dubnium', category: 'transition', atomicNumber: 105 }, { symbol: 'Sg', name: 'Seaborgium', category: 'transition', atomicNumber: 106 }, { symbol: 'Bh', name: 'Bohrium', category: 'transition', atomicNumber: 107 }, { symbol: 'Hs', name: 'Hassium', category: 'transition', atomicNumber: 108 }, { symbol: 'Mt', name: 'Meitnerium', category: 'transition', atomicNumber: 109 }, { symbol: 'Ds', name: 'Darmstadtium', category: 'transition', atomicNumber: 110 }, { symbol: 'Rg', name: 'Roentgenium', category: 'transition', atomicNumber: 111 }, { symbol: 'Cn', name: 'Copernicium', category: 'post-transition', atomicNumber: 112 }, { symbol: 'Nh', name: 'Nihonium', category: 'post-transition', atomicNumber: 113 }, { symbol: 'Fl', name: 'Flerovium', category: 'post-transition', atomicNumber: 114 }, { symbol: 'Mc', name: 'Moscovium', category: 'post-transition', atomicNumber: 115 }, { symbol: 'Lv', name: 'Livermorium', category: 'post-transition', atomicNumber: 116 }, { symbol: 'Ts', name: 'Tennessine', category: 'halogen', atomicNumber: 117 }, { symbol: 'Og', name: 'Oganesson', category: 'noble-gas', atomicNumber: 118 }],
      // Empty row for spacing
      [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null],
      // Lanthanides
      [null, null, { symbol: 'La', name: 'Lanthanum', category: 'lanthanide', atomicNumber: 57 }, { symbol: 'Ce', name: 'Cerium', category: 'lanthanide', atomicNumber: 58 }, { symbol: 'Pr', name: 'Praseodymium', category: 'lanthanide', atomicNumber: 59 }, { symbol: 'Nd', name: 'Neodymium', category: 'lanthanide', atomicNumber: 60 }, { symbol: 'Pm', name: 'Promethium', category: 'lanthanide', atomicNumber: 61 }, { symbol: 'Sm', name: 'Samarium', category: 'lanthanide', atomicNumber: 62 }, { symbol: 'Eu', name: 'Europium', category: 'lanthanide', atomicNumber: 63 }, { symbol: 'Gd', name: 'Gadolinium', category: 'lanthanide', atomicNumber: 64 }, { symbol: 'Tb', name: 'Terbium', category: 'lanthanide', atomicNumber: 65 }, { symbol: 'Dy', name: 'Dysprosium', category: 'lanthanide', atomicNumber: 66 }, { symbol: 'Ho', name: 'Holmium', category: 'lanthanide', atomicNumber: 67 }, { symbol: 'Er', name: 'Erbium', category: 'lanthanide', atomicNumber: 68 }, { symbol: 'Tm', name: 'Thulium', category: 'lanthanide', atomicNumber: 69 }, { symbol: 'Yb', name: 'Ytterbium', category: 'lanthanide', atomicNumber: 70 }, { symbol: 'Lu', name: 'Lutetium', category: 'lanthanide', atomicNumber: 71 }, null],
      // Actinides
      [null, null, { symbol: 'Ac', name: 'Actinium', category: 'actinide', atomicNumber: 89 }, { symbol: 'Th', name: 'Thorium', category: 'actinide', atomicNumber: 90 }, { symbol: 'Pa', name: 'Protactinium', category: 'actinide', atomicNumber: 91 }, { symbol: 'U', name: 'Uranium', category: 'actinide', atomicNumber: 92 }, { symbol: 'Np', name: 'Neptunium', category: 'actinide', atomicNumber: 93 }, { symbol: 'Pu', name: 'Plutonium', category: 'actinide', atomicNumber: 94 }, { symbol: 'Am', name: 'Americium', category: 'actinide', atomicNumber: 95 }, { symbol: 'Cm', name: 'Curium', category: 'actinide', atomicNumber: 96 }, { symbol: 'Bk', name: 'Berkelium', category: 'actinide', atomicNumber: 97 }, { symbol: 'Cf', name: 'Californium', category: 'actinide', atomicNumber: 98 }, { symbol: 'Es', name: 'Einsteinium', category: 'actinide', atomicNumber: 99 }, { symbol: 'Fm', name: 'Fermium', category: 'actinide', atomicNumber: 100 }, { symbol: 'Md', name: 'Mendelevium', category: 'actinide', atomicNumber: 101 }, { symbol: 'No', name: 'Nobelium', category: 'actinide', atomicNumber: 102 }, { symbol: 'Lr', name: 'Lawrencium', category: 'actinide', atomicNumber: 103 }, null]
    ];
  
    return (
      <div className="relative">
        {/* Group numbers */}
        <div className="flex mb-1 text-xs text-gray-500">
          <div className="w-8" /> {/* Spacing for period numbers column */}
          <div className="flex gap-1">
            {Array.from({ length: 18 }, (_, i) => i + 1).map((group) => (
              <div key={group} className="w-[50px] sm:w-[55px] text-center">
                {group}
              </div>
            ))}
          </div>
        </div>
  
        {/* Period numbers and table */}
        <div className="flex">
          {/* Period numbers */}
          <div className="w-8 flex flex-col text-xs text-gray-500">
            {[1, 2, 3, 4, 5, 6, 7].map((period) => (
              <div key={period} className="h-[51px] sm:h-[56px] flex items-center justify-center">
                {period}
              </div>
            ))}
          </div>
  
          {/* Table content */}
          <div className="flex-1">
            {elements.map((row, rowIndex) => (
              <div key={rowIndex} className="flex gap-1 mb-1">
                {row.map((element, colIndex) => (
                  element ? (
                    <button
                      key={element.symbol}
                      onClick={() => {
                        setElementDetails({
                          symbol: element.symbol,
                          name: element.name,
                          atomicNumber: typeof element.atomicNumber === 'string' ? parseInt(element.atomicNumber.split('-')[0]) : element.atomicNumber,
                          atomicMass: atomicMasses[element.symbol] || 0,
                          category: element.category
                        });
                        setElementDialogOpen(true);
                      }}
                      className={`p-1 rounded text-center hover:scale-105 transition-transform w-[50px] h-[50px] sm:w-[55px] sm:h-[55px] flex flex-col justify-between items-center relative
                        ${element.category === 'nonmetal' ? 'bg-yellow-100 hover:bg-yellow-200 dark:text-black' : ''}
                        ${element.category === 'noble-gas' ? 'bg-purple-100 hover:bg-purple-200 dark:text-black' : ''}
                        ${element.category === 'alkali-metal' ? 'bg-red-100 hover:bg-red-200 dark:text-black' : ''}
                        ${element.category === 'alkaline-earth' ? 'bg-orange-100 hover:bg-orange-200 dark:text-black' : ''}
                        ${element.category === 'metalloid' ? 'bg-green-100 hover:bg-green-200 dark:text-black' : ''}
                        ${element.category === 'halogen' ? 'bg-blue-100 hover:bg-blue-200 dark:text-black' : ''}
                        ${element.category === 'transition' ? 'bg-pink-100 hover:bg-pink-200 dark:text-black' : ''}
                        ${element.category === 'post-transition' ? 'bg-indigo-100 hover:bg-indigo-200 dark:text-black' : ''}
                        ${element.category === 'lanthanide' ? 'bg-teal-100 hover:bg-teal-200 dark:text-black' : ''}
                        ${element.category === 'actinide' ? 'bg-cyan-100 hover:bg-cyan-200 dark:text-black' : ''}
                        ${element.category === 'lanthanide-label' ? 'bg-teal-50 hover:bg-teal-100 dark:text-black' : ''}
                        ${element.category === 'actinide-label' ? 'bg-cyan-50 hover:bg-cyan-100 dark:text-black' : ''}`}
                    >
                      <div className="absolute top-0 left-1 text-[8px] text-gray-500 dark:text-gray-700">{element.atomicNumber}</div>
                      <div className="text-[14px] font-mono font-bold mt-2">{element.symbol}</div>
                      <div className="w-full flex justify-between items-end">
                        <div className="text-[8px] text-gray-600 dark:text-gray-800 truncate max-w-[60%]">{element.name}</div>
                        <div className="text-[8px] text-gray-500 dark:text-gray-700">{Math.round(atomicMasses[element.symbol] || 0)}</div>
                      </div>
                    </button>
                  ) : (
                    <div key={colIndex} className="w-[50px] sm:w-[55px]" />
                  )
                ))}
              </div>
            ))}
          </div>
        </div>
  
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-5 gap-2">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-yellow-100" />
            <span className="text-xs text-gray-600">Nonmetal</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-purple-100" />
            <span className="text-xs text-gray-600">Noble Gas</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-red-100" />
            <span className="text-xs text-gray-600">Alkali Metal</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-orange-100" />
            <span className="text-xs text-gray-600">Alkaline Earth</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-green-100" />
            <span className="text-xs text-gray-600">Metalloid</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-blue-100" />
            <span className="text-xs text-gray-600">Halogen</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-pink-100" />
            <span className="text-xs text-gray-600">Transition Metal</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-indigo-100" />
            <span className="text-xs text-gray-600">Post-Transition</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-teal-100" />
            <span className="text-xs text-gray-600">Lanthanide</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-cyan-100" />
            <span className="text-xs text-gray-600">Actinide</span>
          </div>
        </div>
  
        <Dialog open={elementDialogOpen} onOpenChange={setElementDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Element Details</DialogTitle>
            </DialogHeader>
            {elementDetails && (
              <div className="grid gap-4 py-4">
                <div className="flex flex-col items-center gap-4">
                  <div className={`w-24 h-24 rounded-lg flex items-center justify-center text-4xl font-bold font-mono
                    ${elementDetails.category === 'nonmetal' ? 'bg-yellow-100 dark:text-black' : ''}
                    ${elementDetails.category === 'noble-gas' ? 'bg-purple-100 dark:text-black' : ''}
                    ${elementDetails.category === 'alkali-metal' ? 'bg-red-100 dark:text-black' : ''}
                    ${elementDetails.category === 'alkaline-earth' ? 'bg-orange-100 dark:text-black' : ''}
                    ${elementDetails.category === 'metalloid' ? 'bg-green-100 dark:text-black' : ''}
                    ${elementDetails.category === 'halogen' ? 'bg-blue-100 dark:text-black' : ''}
                    ${elementDetails.category === 'transition' ? 'bg-pink-100 dark:text-black' : ''}
                    ${elementDetails.category === 'post-transition' ? 'bg-indigo-100 dark:text-black' : ''}
                    ${elementDetails.category === 'lanthanide' ? 'bg-teal-100 dark:text-black' : ''}
                    ${elementDetails.category === 'actinide' ? 'bg-cyan-100 dark:text-black' : ''}`}
                  >
                    {elementDetails.symbol}
                  </div>
                  <div className="text-2xl font-semibold dark:text-white">{elementDetails.name}</div>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex flex-col gap-1">
                    <div className="font-medium text-muted-foreground">Atomic Number</div>
                    <div className="font-mono dark:text-white">{elementDetails.atomicNumber}</div>
                  </div>
                  <div className="flex flex-col gap-1">
                    <div className="font-medium text-muted-foreground">Atomic Mass</div>
                    <div className="font-mono dark:text-white">{Math.round(elementDetails.atomicMass)}</div>
                  </div>
                  <div className="flex flex-col gap-1">
                    <div className="font-medium text-muted-foreground">Category</div>
                    <div className="font-mono capitalize dark:text-white">{elementDetails.category}</div>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center transition-colors hover:bg-white/10"
        >
          <Calculator className="h-5 w-5 md:h-6 md:w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] md:max-w-[1000px] lg:max-w-[1200px] max-h-[80vh] md:max-h-[calc(100vh-2rem)] overflow-auto">
        <DialogHeader>
          <DialogTitle>Tools</DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="calculator" className="mt-4">
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="calculator" className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              <span>Calculator</span>
            </TabsTrigger>
            <TabsTrigger value="graph" className="flex items-center gap-2">
              <LineChart className="h-4 w-4" />
              <span>Graph</span>
            </TabsTrigger>
            <TabsTrigger value="convert" className="flex items-center gap-2">
              <Ruler className="h-4 w-4" />
              <span>Convert</span>
            </TabsTrigger>
            <TabsTrigger value="chemistry" className="flex items-center gap-2">
              <Atom className="h-4 w-4" />
              <span>Chemistry</span>
            </TabsTrigger>
          </TabsList>
          
          <div>
            <TabsContent value="calculator">
              <div className="pt-4 space-y-4">
                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsScientific(!isScientific)}
                    className="mb-2"
                  >
                    {isScientific ? "Basic" : "Scientific"}
                  </Button>
                </div>
                <div className="bg-secondary p-4 rounded-lg">
                  <div className="text-right text-2xl font-mono mb-4">{calculatorDisplay}</div>
                  <div className="grid grid-cols-4 gap-2">
                    {isScientific && (
                      <>
                        <Button variant="outline" onClick={() => handleScientificOperation('sin')}>sin</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('cos')}>cos</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('tan')}>tan</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('log')}>log</Button>
                        
                        <Button variant="outline" onClick={() => handleScientificOperation('ln')}>ln</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('square')}>x²</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('sqrt')}>√</Button>
                        <Button variant="outline" onClick={() => handleOperation('pow')}>x^y</Button>
                        
                        <Button variant="outline" onClick={() => handleScientificOperation('pi')}>π</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('e')}>e</Button>
                        <Button variant="outline" onClick={() => handleScientificOperation('percent')}><Percent className="h-4 w-4" /></Button>
                        <Button variant="outline" onClick={() => handleOperation('/')}><Divide className="h-4 w-4" /></Button>
                        
                        <Separator className="col-span-4 my-2" />
                      </>
                    )}
                    
                    <Button variant="outline" onClick={() => handleNumber('7')}>7</Button>
                    <Button variant="outline" onClick={() => handleNumber('8')}>8</Button>
                    <Button variant="outline" onClick={() => handleNumber('9')}>9</Button>
                    <Button variant="outline" onClick={() => handleOperation('/')}><Divide className="h-4 w-4" /></Button>
                    
                    <Button variant="outline" onClick={() => handleNumber('4')}>4</Button>
                    <Button variant="outline" onClick={() => handleNumber('5')}>5</Button>
                    <Button variant="outline" onClick={() => handleNumber('6')}>6</Button>
                    <Button variant="outline" onClick={() => handleOperation('*')}><X className="h-4 w-4" /></Button>
                    
                    <Button variant="outline" onClick={() => handleNumber('1')}>1</Button>
                    <Button variant="outline" onClick={() => handleNumber('2')}>2</Button>
                    <Button variant="outline" onClick={() => handleNumber('3')}>3</Button>
                    <Button variant="outline" onClick={() => handleOperation('-')}><Minus className="h-4 w-4" /></Button>
                    
                    <Button variant="outline" onClick={() => handleNumber('0')}>0</Button>
                    <Button variant="outline" onClick={() => handleNumber('.')}>.</Button>
                    <Button variant="outline" onClick={handleEqual}><Equal className="h-4 w-4" /></Button>
                    <Button variant="outline" onClick={() => handleOperation('+')}><Plus className="h-4 w-4" /></Button>
                    
                    <Button variant="outline" onClick={handleClear} className="col-span-4">
                      <RefreshCw className="h-4 w-4 mr-2" /> Clear
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="graph">
              <div className="flex flex-col space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={expression}
                    onChange={(e) => setExpression(e.target.value)}
                    placeholder="Enter function (e.g., y=x^2)"
                    className="flex-1"
                  />
                  <Button onClick={() => {}}>Plot</Button>
                </div>
                <div className="h-[300px] w-full border rounded-lg overflow-hidden">
                  <DesmosGraph expression={expression} height="300px" />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="convert">
              <div className="pt-4 space-y-4">
                <div className="flex gap-2">
                  <Input
                    type="number"
                    value={convertValue}
                    onChange={(e) => {
                      setConvertValue(e.target.value);
                      setConvertResult(null);
                    }}
                    placeholder="Enter value"
                    className="flex-1"
                  />
                  <Select value={fromUnit} onValueChange={setFromUnit}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="m">m</SelectItem>
                      <SelectItem value="km">km</SelectItem>
                      <SelectItem value="cm">cm</SelectItem>
                      <SelectItem value="mm">mm</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="flex items-center">to</span>
                  <Select value={toUnit} onValueChange={setToUnit}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="m">m</SelectItem>
                      <SelectItem value="km">km</SelectItem>
                      <SelectItem value="cm">cm</SelectItem>
                      <SelectItem value="mm">mm</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={handleConvert}>Convert</Button>
                </div>
                {convertResult && (
                  <div className="bg-secondary/20 p-3 rounded-lg text-center font-mono">
                    {convertResult}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="chemistry">
              <div className="space-y-4 overflow-x-auto pb-2 w-full">
                <div className="min-w-max">
                  {renderPeriodicTable()}
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};