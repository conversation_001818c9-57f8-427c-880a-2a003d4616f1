import { useEffect, useState } from "react"
// TODO: Implement Supabase user analytics
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import {
  <PERSON>Chart,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from "recharts"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useTheme } from "@/hooks/use-theme" // Import useTheme hook

interface StudySession {
  subject: string
  duration: number
  mode: "pomodoro" | "stopwatch"
  phase: "work" | "shortBreak" | "longBreak"
  completed: boolean
  date: string
  weekNumber: number
  month: string
  year: number
}

interface UserStats {
  totalStudyTime: number
  completedPomodoros: number
  totalSubjects: number
  studyStreak: number
  subjectDistribution: {
    subject: string
    totalDuration: number
    completedPomodoros: number
  }[]
  dailyProgress: {
    date: string
    totalDuration: number
    completedPomodoros: number
  }[]
  weeklyProgress: {
    weekNumber: number
    totalDuration: number
    completedPomodoros: number
  }[]
  monthlyProgress: {
    month: string
    totalDuration: number
    completedPomodoros: number
  }[]
}

interface ComparisonData {
  current: {
    totalStudyTime: number
    completedPomodoros: number
    averageDailyTime: number
  }
  previous: {
    totalStudyTime: number
    completedPomodoros: number
    averageDailyTime: number
  }
  percentChange: {
    totalStudyTime: number
    completedPomodoros: number
    averageDailyTime: number
  }
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d']

export function UserAnalytics({ userId }: { userId: string }) {
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [comparisonPeriod, setComparisonPeriod] = useState<"week" | "month">("week")
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null)
  const { theme } = useTheme() // Get current theme

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const userDoc = await getDoc(doc(db, "users", userId))
        if (!userDoc.exists()) return

        const studySessions = userDoc.data().studySessions || {}
        const sessions = Object.values(studySessions) as StudySession[]

        const stats = processUserStats(sessions)
        setUserStats(stats)
      } catch (error) {
        console.error("Error fetching user stats:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserStats()
  }, [userId])

  // Generate comparison data when userStats changes or comparison period changes
  useEffect(() => {
    if (!userStats) return

    generateComparisonData()
  }, [userStats, comparisonPeriod])

  const generateComparisonData = () => {
    if (!userStats) return

    let currentData: any[] = []
    let previousData: any[] = []

    if (comparisonPeriod === "week") {
      // Get the last 7 days and previous 7 days
      const sortedDailyData = [...userStats.dailyProgress].sort((a, b) =>
        new Date(b.date).getTime() - new Date(a.date).getTime()
      )

      currentData = sortedDailyData.slice(0, 7)
      previousData = sortedDailyData.slice(7, 14)
    } else {
      // Get the last 30 days and previous 30 days
      const sortedDailyData = [...userStats.dailyProgress].sort((a, b) =>
        new Date(b.date).getTime() - new Date(a.date).getTime()
      )

      currentData = sortedDailyData.slice(0, 30)
      previousData = sortedDailyData.slice(30, 60)
    }

    // Calculate totals
    const currentTotalTime = currentData.reduce((sum, day) => sum + day.totalDuration, 0)
    const previousTotalTime = previousData.reduce((sum, day) => sum + day.totalDuration, 0)

    const currentPomodoros = currentData.reduce((sum, day) => sum + day.completedPomodoros, 0)
    const previousPomodoros = previousData.reduce((sum, day) => sum + day.completedPomodoros, 0)

    const currentAvgDaily = currentData.length > 0 ? currentTotalTime / currentData.length : 0
    const previousAvgDaily = previousData.length > 0 ? previousTotalTime / previousData.length : 0

    // Calculate percent changes
    const calculatePercentChange = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0
      return ((current - previous) / previous) * 100
    }

    const timePercentChange = calculatePercentChange(currentTotalTime, previousTotalTime)
    const pomodorosPercentChange = calculatePercentChange(currentPomodoros, previousPomodoros)
    const avgDailyPercentChange = calculatePercentChange(currentAvgDaily, previousAvgDaily)

    setComparisonData({
      current: {
        totalStudyTime: currentTotalTime,
        completedPomodoros: currentPomodoros,
        averageDailyTime: currentAvgDaily
      },
      previous: {
        totalStudyTime: previousTotalTime,
        completedPomodoros: previousPomodoros,
        averageDailyTime: previousAvgDaily
      },
      percentChange: {
        totalStudyTime: timePercentChange,
        completedPomodoros: pomodorosPercentChange,
        averageDailyTime: avgDailyPercentChange
      }
    })
  }

  const processUserStats = (sessions: StudySession[]): UserStats => {
    const stats: UserStats = {
      totalStudyTime: 0,
      completedPomodoros: 0,
      totalSubjects: 0,
      studyStreak: 0,
      subjectDistribution: [],
      dailyProgress: [],
      weeklyProgress: [],
      monthlyProgress: []
    }

    const subjectMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const dailyMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const weeklyMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const monthlyMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()

    sessions.forEach(session => {
      // Update total study time
      stats.totalStudyTime += session.duration

      // Update completed pomodoros
      if (session.completed && session.mode === "pomodoro") {
        stats.completedPomodoros++
      }

      // Update subject distribution
      if (!subjectMap.has(session.subject)) {
        subjectMap.set(session.subject, { totalDuration: 0, completedPomodoros: 0 })
      }
      const subjectStats = subjectMap.get(session.subject)!
      subjectStats.totalDuration += session.duration
      if (session.completed && session.mode === "pomodoro") {
        subjectStats.completedPomodoros++
      }

      // Update daily progress
      if (!dailyMap.has(session.date)) {
        dailyMap.set(session.date, { totalDuration: 0, completedPomodoros: 0 })
      }
      const dailyStats = dailyMap.get(session.date)!
      dailyStats.totalDuration += session.duration
      if (session.completed && session.mode === "pomodoro") {
        dailyStats.completedPomodoros++
      }

      // Update weekly progress
      const weekKey = `${session.year}-W${session.weekNumber}`
      if (!weeklyMap.has(weekKey)) {
        weeklyMap.set(weekKey, { totalDuration: 0, completedPomodoros: 0 })
      }
      const weeklyStats = weeklyMap.get(weekKey)!
      weeklyStats.totalDuration += session.duration
      if (session.completed && session.mode === "pomodoro") {
        weeklyStats.completedPomodoros++
      }

      // Update monthly progress
      if (!monthlyMap.has(session.month)) {
        monthlyMap.set(session.month, { totalDuration: 0, completedPomodoros: 0 })
      }
      const monthlyStats = monthlyMap.get(session.month)!
      monthlyStats.totalDuration += session.duration
      if (session.completed && session.mode === "pomodoro") {
        monthlyStats.completedPomodoros++
      }
    })

    // Convert maps to arrays
    stats.subjectDistribution = Array.from(subjectMap.entries()).map(([subject, data]) => ({
      subject,
      ...data
    }))
    stats.totalSubjects = stats.subjectDistribution.length

    stats.dailyProgress = Array.from(dailyMap.entries()).map(([date, data]) => ({
      date,
      ...data
    })).sort((a, b) => a.date.localeCompare(b.date))

    stats.weeklyProgress = Array.from(weeklyMap.entries()).map(([weekKey, data]) => ({
      weekNumber: parseInt(weekKey.split('-W')[1]),
      weekDisplay: `Week ${weekKey.split('-W')[1]}`,
      ...data
    })).sort((a, b) => a.weekNumber - b.weekNumber)

    stats.monthlyProgress = Array.from(monthlyMap.entries()).map(([month, data]) => ({
      month,
      ...data
    })).sort((a, b) => a.month.localeCompare(b.month))

    // Calculate study streak
    let currentStreak = 0
    let maxStreak = 0
    let lastDate = ''
    stats.dailyProgress.forEach(day => {
      if (lastDate === '') {
        currentStreak = 1
      } else {
        const dayDiff = Math.floor((new Date(day.date).getTime() - new Date(lastDate).getTime()) / (1000 * 60 * 60 * 24))
        if (dayDiff === 1) {
          currentStreak++
        } else {
          currentStreak = 1
        }
      }
      maxStreak = Math.max(maxStreak, currentStreak)
      lastDate = day.date
    })
    stats.studyStreak = maxStreak

    return stats
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    if (minutes > 0) {
      return `${minutes}m`
    }
    return `0m`
  }

  const formatDateDisplay = (dateString: string): string => {
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear().toString().slice(2)}`;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  if (!userStats) {
    return (
      <div className="flex items-center justify-center min-h-[400px] text-muted-foreground dark:text-white/60">
        No study data available
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Theme-aware Summary Card */}
        <Card className="bg-card border-border shadow-lg hover:shadow-xl transition-shadow dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 dark:border-white/10">
          <CardHeader className="p-4 pb-0">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Study Time</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <p className="text-2xl font-bold text-primary dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-purple-400">
              {formatDuration(userStats.totalStudyTime)}
            </p>
          </CardContent>
        </Card>

        {/* Theme-aware Summary Card */}
        <Card className="bg-card border-border shadow-lg hover:shadow-xl transition-shadow dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 dark:border-white/10">
          <CardHeader className="p-4 pb-0">
            <CardTitle className="text-sm font-medium text-muted-foreground">Completed Pomodoros</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <p className="text-2xl font-bold text-primary dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-pink-400 dark:to-purple-400">
              {userStats.completedPomodoros}
            </p>
          </CardContent>
        </Card>

        {/* Theme-aware Summary Card */}
        <Card className="bg-card border-border shadow-lg hover:shadow-xl transition-shadow dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 dark:border-white/10">
          <CardHeader className="p-4 pb-0">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Subjects</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <p className="text-2xl font-bold text-primary dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-cyan-400 dark:to-blue-400">
              {userStats.totalSubjects}
            </p>
          </CardContent>
        </Card>

        {/* Theme-aware Summary Card */}
        <Card className="bg-card border-border shadow-lg hover:shadow-xl transition-shadow dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 dark:border-white/10">
          <CardHeader className="p-4 pb-0">
            <CardTitle className="text-sm font-medium text-muted-foreground">Study Streak</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <p className="text-2xl font-bold text-primary dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-emerald-400 dark:to-teal-400">
              {userStats.studyStreak} days
            </p>
          </CardContent>
        </Card> {/* This closing tag was missing */}
      </div>

      {/* Comparison Section */}
      {/* Theme-aware Card */}
      <Card className="bg-card border-border rounded-xl shadow-xl hover:shadow-2xl transition-shadow overflow-hidden dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 dark:border-white/5 dark:backdrop-blur-xl">
        {/* Theme-aware CardHeader */}
        <CardHeader className="flex flex-row items-center justify-between border-b border-border dark:border-white/5 pb-4">
          <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
            {/* Theme-aware Title */}
            <span className="text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-pink-400">Progress Comparison</span>
          </CardTitle>
          {/* Theme-aware Select */}
          <Select value={comparisonPeriod} onValueChange={(value) => setComparisonPeriod(value as "week" | "month")}>
            <SelectTrigger className="w-[180px] bg-background text-foreground border-border hover:bg-accent/50 dark:bg-white/5 border dark:border-white/10 dark:text-white/90">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent className="bg-popover border-border shadow-md dark:bg-gradient-to-br dark:from-slate-800/95 dark:to-slate-900/95 border dark:border-white/10">
              <SelectItem value="week" className="text-foreground dark:text-white/90 hover:bg-accent hover:text-accent-foreground dark:hover:bg-white/10">Last 7 days vs Previous</SelectItem>
              <SelectItem value="month" className="text-foreground dark:text-white/90 hover:bg-accent hover:text-accent-foreground dark:hover:bg-white/10">Last 30 days vs Previous</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent className="pt-6">
          {comparisonData ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Theme-aware inner div */}
              <div className="bg-muted/50 dark:bg-white/5 rounded-lg p-4 border dark:border-white/10 hover:border-primary/30 dark:hover:border-indigo-500/30 transition-colors">
                {/* Theme-aware text */}
                <h3 className="text-sm font-medium text-muted-foreground dark:text-white/80 mb-1">Study Time</h3>
                {/* Theme-aware text */}
                <p className="text-2xl font-bold text-primary dark:text-indigo-400">{formatDuration(comparisonData.current.totalStudyTime)}</p>
                <div className="flex items-center mt-2">
                  {comparisonData.percentChange.totalStudyTime > 0 ? (
                    // Theme-aware text (positive change)
                    <div className="flex items-center text-emerald-600 dark:text-emerald-400">
                      <span className="text-xs mr-1">↑</span>
                      <span className="text-xs">{comparisonData.percentChange.totalStudyTime.toFixed(1)}%</span>
                    </div>
                  ) : comparisonData.percentChange.totalStudyTime < 0 ? (
                    // Theme-aware text (negative change)
                    <div className="flex items-center text-rose-600 dark:text-rose-400">
                      <span className="text-xs mr-1">↓</span>
                      <span className="text-xs">{Math.abs(comparisonData.percentChange.totalStudyTime).toFixed(1)}%</span>
                    </div>
                  ) : (
                    // Theme-aware text (no change)
                    <div className="flex items-center text-muted-foreground dark:text-white/60">
                      <span className="text-xs">No change</span>
                    </div>
                  )}
                  {/* Theme-aware text */}
                  <span className="text-xs text-muted-foreground dark:text-white/60 ml-2">vs previous {comparisonPeriod}</span>
                </div>
              </div>

              {/* Theme-aware inner div */}
              <div className="bg-muted/50 dark:bg-white/5 rounded-lg p-4 border dark:border-white/10 hover:border-primary/30 dark:hover:border-pink-500/30 transition-colors">
                {/* Theme-aware text */}
                <h3 className="text-sm font-medium text-muted-foreground dark:text-white/80 mb-1">Completed Pomodoros</h3>
                {/* Theme-aware text */}
                <p className="text-2xl font-bold text-primary dark:text-pink-400">{comparisonData.current.completedPomodoros}</p>
                <div className="flex items-center mt-2">
                  {comparisonData.percentChange.completedPomodoros > 0 ? (
                    // Theme-aware text (positive change)
                    <div className="flex items-center text-emerald-600 dark:text-emerald-400">
                      <span className="text-xs mr-1">↑</span>
                      <span className="text-xs">{comparisonData.percentChange.completedPomodoros.toFixed(1)}%</span>
                    </div>
                  ) : comparisonData.percentChange.completedPomodoros < 0 ? (
                    // Theme-aware text (negative change)
                    <div className="flex items-center text-rose-600 dark:text-rose-400">
                      <span className="text-xs mr-1">↓</span>
                      <span className="text-xs">{Math.abs(comparisonData.percentChange.completedPomodoros).toFixed(1)}%</span>
                    </div>
                  ) : (
                    // Theme-aware text (no change)
                    <div className="flex items-center text-muted-foreground dark:text-white/60">
                      <span className="text-xs">No change</span>
                    </div>
                  )}
                  {/* Theme-aware text */}
                  <span className="text-xs text-muted-foreground dark:text-white/60 ml-2">vs previous {comparisonPeriod}</span>
                </div>
              </div>

              {/* Theme-aware inner div */}
              <div className="bg-muted/50 dark:bg-white/5 rounded-lg p-4 border dark:border-white/10 hover:border-primary/30 dark:hover:border-cyan-500/30 transition-colors">
                {/* Theme-aware text */}
                <h3 className="text-sm font-medium text-muted-foreground dark:text-white/80 mb-1">Average Daily Time</h3>
                {/* Theme-aware text */}
                <p className="text-2xl font-bold text-primary dark:text-cyan-400">{formatDuration(comparisonData.current.averageDailyTime)}</p>
                <div className="flex items-center mt-2">
                  {comparisonData.percentChange.averageDailyTime > 0 ? (
                    // Theme-aware text (positive change)
                    <div className="flex items-center text-emerald-600 dark:text-emerald-400">
                      <span className="text-xs mr-1">↑</span>
                      <span className="text-xs">{comparisonData.percentChange.averageDailyTime.toFixed(1)}%</span>
                    </div>
                  ) : comparisonData.percentChange.averageDailyTime < 0 ? (
                    // Theme-aware text (negative change)
                    <div className="flex items-center text-rose-600 dark:text-rose-400">
                      <span className="text-xs mr-1">↓</span>
                      <span className="text-xs">{Math.abs(comparisonData.percentChange.averageDailyTime).toFixed(1)}%</span>
                    </div>
                  ) : (
                    // Theme-aware text (no change)
                    <div className="flex items-center text-muted-foreground dark:text-white/60">
                      <span className="text-xs">No change</span>
                    </div>
                  )}
                  {/* Theme-aware text */}
                  <span className="text-xs text-muted-foreground dark:text-white/60 ml-2">vs previous {comparisonPeriod}</span>
                </div>
              </div>
            </div>
          ) : (
            // Theme-aware text
            <p className="text-center text-muted-foreground dark:text-white/60 py-4">Not enough data for comparison</p>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="w-full">
        {/* Theme-aware TabsList */}
        <TabsList className="bg-muted border-border dark:bg-gradient-to-r dark:from-slate-800/60 dark:to-slate-900/60 p-1.5 gap-1 inline-flex flex-wrap justify-center w-full max-w-[600px] mx-auto sm:flex-nowrap rounded-xl border dark:border-white/5 shadow-xl dark:backdrop-blur-xl mb-8">
          {/* Theme-aware TabsTrigger */}
          <TabsTrigger
            value="overview"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground dark:data-[state=active]:bg-gradient-to-r dark:data-[state=active]:from-indigo-600/90 dark:data-[state=active]:to-indigo-700/90 dark:data-[state=active]:text-white text-muted-foreground dark:text-white/80 text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 flex-1 rounded-lg transition-all duration-200"
          >
            Overview
          </TabsTrigger>
          {/* Theme-aware TabsTrigger */}
          <TabsTrigger
            value="subjects"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground dark:data-[state=active]:bg-gradient-to-r dark:data-[state=active]:from-pink-600/90 dark:data-[state=active]:to-purple-700/90 dark:data-[state=active]:text-white text-muted-foreground dark:text-white/80 text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 flex-1 rounded-lg transition-all duration-200"
          >
            Subjects
          </TabsTrigger>
          {/* Theme-aware TabsTrigger */}
          <TabsTrigger
            value="progress"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground dark:data-[state=active]:bg-gradient-to-r dark:data-[state=active]:from-indigo-600/90 dark:data-[state=active]:to-indigo-700/90 dark:data-[state=active]:text-white text-muted-foreground dark:text-white/80 text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 flex-1 rounded-lg transition-all duration-200"
          >
            Progress
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Theme-aware Card */}
            <Card className="bg-card border-border rounded-xl shadow-xl hover:shadow-2xl transition-shadow overflow-hidden dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 dark:border-white/5 dark:backdrop-blur-xl">
              {/* Theme-aware CardHeader */}
              <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
                  {/* Theme-aware Title */}
                  <span className="text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-pink-400">Subject Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-[400px] pt-6">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={userStats.subjectDistribution}
                      dataKey="totalDuration"
                      nameKey="subject"
                      cx="50%"
                      cy="50%"
                      outerRadius={150}
                      // Theme-aware label fill
                      label={({ subject }) => (
                        <text fill={theme === 'dark' ? 'white' : 'hsl(var(--foreground))'} fontSize={12}>
                          {subject}
                        </text>
                      )}
                    >
                      {userStats.subjectDistribution.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    {/* Theme-aware Tooltip */}
                    <Tooltip
                      formatter={(value: number) => formatDuration(value)}
                      contentStyle={theme === 'dark'
                        ? { backgroundColor: '#1a1f3c', border: '1px solid rgba(255,255,255,0.2)', color: 'white' }
                        : { backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))', color: 'hsl(var(--foreground))' }
                      }
                      labelStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                      itemStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Legend */}
                    <Legend wrapperStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Theme-aware Card */}
            <Card className="bg-card border-border rounded-xl shadow-xl hover:shadow-2xl transition-shadow overflow-hidden dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 dark:border-white/5 dark:backdrop-blur-xl">
              {/* Theme-aware CardHeader */}
              <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
                  {/* Theme-aware Title */}
                  <span className="text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-pink-400">Daily Progress (Last 7 Days)</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-[400px] pt-6">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={userStats.dailyProgress.slice(-7)}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    {/* Theme-aware CartesianGrid */}
                    <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? "rgba(255,255,255,0.1)" : "hsl(var(--border))"} />
                    {/* Theme-aware XAxis */}
                    <XAxis
                      dataKey="date"
                      stroke={theme === 'dark' ? "rgba(255,255,255,0.6)" : "hsl(var(--muted-foreground))"}
                      tick={{ fill: theme === 'dark' ? 'white' : 'hsl(var(--foreground))' }}
                      tickFormatter={formatDateDisplay}
                    />
                    {/* Theme-aware YAxis */}
                    <YAxis
                      stroke={theme === 'dark' ? "rgba(255,255,255,0.6)" : "hsl(var(--muted-foreground))"}
                      tick={{ fill: theme === 'dark' ? 'white' : 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Tooltip */}
                    <Tooltip
                      formatter={(value: number) => formatDuration(value)}
                      labelFormatter={(value) => formatDateDisplay(value as string)}
                      contentStyle={theme === 'dark'
                        ? { backgroundColor: '#1a1f3c', border: '1px solid rgba(255,255,255,0.2)', color: 'white' }
                        : { backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))', color: 'hsl(var(--foreground))' }
                      }
                      labelStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                      itemStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Legend */}
                    <Legend wrapperStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }} />
                    <Line type="monotone" dataKey="totalDuration" stroke="#8884d8" name="Study Time" />
                    <Line type="monotone" dataKey="completedPomodoros" stroke="#82ca9d" name="Pomodoros" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="subjects">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {userStats.subjectDistribution.map((subject) => (
              // Theme-aware subject card
              <Card
                key={subject.subject}
                className="flex flex-col p-5 bg-card border-border hover:bg-accent/50 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] group dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 dark:hover:from-slate-700/80 dark:hover:to-slate-800/80 dark:border-white/10"
              >
                {/* Theme-aware title */}
                <h3 className="text-lg font-medium text-foreground dark:group-hover:text-white transition-colors">{subject.subject}</h3>
                <div className="my-4 space-y-4">
                  <div className="flex justify-between items-center">
                    {/* Theme-aware text */}
                    <span className="text-muted-foreground dark:text-white/60">Total Time</span>
                    {/* Theme-aware text */}
                    <span className="text-xl font-semibold text-primary dark:text-indigo-400">
                      {formatDuration(subject.totalDuration)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    {/* Theme-aware text */}
                    <span className="text-muted-foreground dark:text-white/60">Completed Pomodoros</span>
                    {/* Theme-aware text */}
                    <span className="text-xl font-semibold text-primary dark:text-pink-400">
                      {subject.completedPomodoros}
                    </span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="progress">
          <div className="space-y-6">
            {/* Theme-aware Card */}
            <Card className="bg-card border-border rounded-xl shadow-xl hover:shadow-2xl transition-shadow overflow-hidden dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 dark:border-white/5 dark:backdrop-blur-xl">
              {/* Theme-aware CardHeader */}
              <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
                  {/* Theme-aware Title */}
                  <span className="text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-pink-400">Weekly Progress</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-[400px] pt-6">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={userStats.weeklyProgress}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    {/* Theme-aware CartesianGrid */}
                    <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? "rgba(255,255,255,0.1)" : "hsl(var(--border))"} />
                    {/* Theme-aware XAxis */}
                    <XAxis
                      dataKey="weekDisplay"
                      stroke={theme === 'dark' ? "rgba(255,255,255,0.6)" : "hsl(var(--muted-foreground))"}
                      tick={{ fill: theme === 'dark' ? 'white' : 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware YAxis */}
                    <YAxis
                      stroke={theme === 'dark' ? "rgba(255,255,255,0.6)" : "hsl(var(--muted-foreground))"}
                      tick={{ fill: theme === 'dark' ? 'white' : 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Tooltip */}
                    <Tooltip
                      formatter={(value: number) => formatDuration(value)}
                      contentStyle={theme === 'dark'
                        ? { backgroundColor: '#1a1f3c', border: '1px solid rgba(255,255,255,0.2)', color: 'white' }
                        : { backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))', color: 'hsl(var(--foreground))' }
                      }
                      labelStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                      itemStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Legend */}
                    <Legend wrapperStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }} />
                    <Line type="monotone" dataKey="totalDuration" stroke="#8884d8" name="Study Time" />
                    <Line type="monotone" dataKey="completedPomodoros" stroke="#82ca9d" name="Pomodoros" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Theme-aware Card */}
            <Card className="bg-card border-border rounded-xl shadow-xl hover:shadow-2xl transition-shadow overflow-hidden dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 dark:border-white/5 dark:backdrop-blur-xl">
              {/* Theme-aware CardHeader */}
              <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
                  {/* Theme-aware Title */}
                  <span className="text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-pink-400">Monthly Progress</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-[400px] pt-6">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={userStats.monthlyProgress}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    {/* Theme-aware CartesianGrid */}
                    <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? "rgba(255,255,255,0.1)" : "hsl(var(--border))"} />
                    {/* Theme-aware XAxis */}
                    <XAxis
                      dataKey="month"
                      stroke={theme === 'dark' ? "rgba(255,255,255,0.6)" : "hsl(var(--muted-foreground))"}
                      tick={{ fill: theme === 'dark' ? 'white' : 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware YAxis */}
                    <YAxis
                      stroke={theme === 'dark' ? "rgba(255,255,255,0.6)" : "hsl(var(--muted-foreground))"}
                      tick={{ fill: theme === 'dark' ? 'white' : 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Tooltip */}
                    <Tooltip
                      formatter={(value: number) => formatDuration(value)}
                      contentStyle={theme === 'dark'
                        ? { backgroundColor: '#1a1f3c', border: '1px solid rgba(255,255,255,0.2)', color: 'white' }
                        : { backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))', color: 'hsl(var(--foreground))' }
                      }
                      labelStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                      itemStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }}
                    />
                    {/* Theme-aware Legend */}
                    <Legend wrapperStyle={theme === 'dark' ? { color: 'white' } : { color: 'hsl(var(--foreground))' }} />
                    <Line type="monotone" dataKey="totalDuration" stroke="#8884d8" name="Study Time" />
                    <Line type="monotone" dataKey="completedPomodoros" stroke="#82ca9d" name="Pomodoros" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
