import { useEffect, useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON>U<PERSON>, ArrowDown } from "lucide-react"

interface StudySession {
  subject: string
  duration: number
  mode: "pomodoro" | "stopwatch"
  phase: "work" | "shortBreak" | "longBreak"
  completed: boolean
  date: string
  weekNumber: number
  month: string
  year: number
}

interface UserStats {
  totalStudyTime: number
  completedPomodoros: number
  totalSubjects: number
  studyStreak: number
  subjectDistribution: {
    subject: string
    totalDuration: number
    completedPomodoros: number
  }[]
  dailyProgress: {
    date: string
    totalDuration: number
    completedPomodoros: number
    subjectDurations?: { [key: string]: number }
  }[]
  avgFocusPerDay: number
  todaySubjects: number
  todayFocusPercentage: number
}

export function SimpleUserAnalytics({ userId }: { userId: string }) {
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const userDoc = await getDoc(doc(db, "users", userId))
        if (!userDoc.exists()) return

        const studySessions = userDoc.data().studySessions || {}
        const sessions = Object.values(studySessions) as StudySession[]

        const stats = processUserStats(sessions)
        setUserStats(stats)
      } catch (error) {
        console.error("Error fetching user stats:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserStats()
  }, [userId])

  const processUserStats = (sessions: StudySession[]): UserStats => {
    const stats: UserStats = {
      totalStudyTime: 0,
      completedPomodoros: 0,
      totalSubjects: 0,
      studyStreak: 0,
      subjectDistribution: [],
      dailyProgress: [],
      avgFocusPerDay: 0,
      todaySubjects: 0,
      todayFocusPercentage: 0
    }

    const subjectMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const dailyMap = new Map<string, { totalDuration: number; completedPomodoros: number; subjectDurations: { [key: string]: number } }>()

    sessions.forEach(session => {
      // Update total study time
      stats.totalStudyTime += session.duration

      // Update completed pomodoros
      if (session.completed && session.mode === "pomodoro") {
        stats.completedPomodoros++
      }

      // Update subject distribution
      if (!subjectMap.has(session.subject)) {
        subjectMap.set(session.subject, { totalDuration: 0, completedPomodoros: 0 })
      }
      const subjectStats = subjectMap.get(session.subject)!
      subjectStats.totalDuration += session.duration
      if (session.completed && session.mode === "pomodoro") {
        subjectStats.completedPomodoros++
      }

      // Update daily progress for streak calculation
      if (!dailyMap.has(session.date)) {
        dailyMap.set(session.date, { totalDuration: 0, completedPomodoros: 0, subjectDurations: {} })
      }
      const dailyStats = dailyMap.get(session.date)!
      dailyStats.totalDuration += session.duration
      if (session.completed && session.mode === "pomodoro") {
        dailyStats.completedPomodoros++
      }

      // Track subject durations per day
      if (!dailyStats.subjectDurations[session.subject]) {
        dailyStats.subjectDurations[session.subject] = 0
      }
      dailyStats.subjectDurations[session.subject] += session.duration
    })

    // Convert subject map to array
    stats.subjectDistribution = Array.from(subjectMap.entries()).map(([subject, data]) => ({
      subject,
      ...data
    }))
    stats.totalSubjects = stats.subjectDistribution.length

    // Convert daily map to array
    stats.dailyProgress = Array.from(dailyMap.entries()).map(([date, data]) => ({
      date,
      totalDuration: data.totalDuration,
      completedPomodoros: data.completedPomodoros,
      subjectDurations: data.subjectDurations
    })).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    // Calculate study streak
    const dates = Array.from(dailyMap.keys()).sort()
    let currentStreak = 0
    let maxStreak = 0
    let lastDate = ''

    dates.forEach(date => {
      if (lastDate === '') {
        currentStreak = 1
      } else {
        const dayDiff = Math.floor(
          (new Date(date).getTime() - new Date(lastDate).getTime()) / (1000 * 60 * 60 * 24)
        )
        if (dayDiff === 1) {
          currentStreak++
        } else {
          currentStreak = 1
        }
      }
      maxStreak = Math.max(maxStreak, currentStreak)
      lastDate = date
    })
    stats.studyStreak = maxStreak

    // Calculate average focus per day
    if (stats.dailyProgress.length > 0) {
      stats.avgFocusPerDay = stats.totalStudyTime / stats.dailyProgress.length
    }

    // Calculate today's stats
    // Format date in local time zone (YYYY-MM-DD)
    const formatDateToLocalYYYYMMDD = (date: Date): string => {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const today = formatDateToLocalYYYYMMDD(new Date())
    const todayStats = stats.dailyProgress.find(day => day.date === today)

    if (todayStats) {
      stats.todaySubjects = todayStats.subjectDurations ? Object.keys(todayStats.subjectDurations).length : 0
      // Assuming 8 hours is a full study day (480 minutes)
      stats.todayFocusPercentage = Math.min(100, (todayStats.totalDuration / 480) * 100)
    }

    return stats
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    }
    if (minutes > 0) {
      return `${minutes}m ${secs}s`
    }
    return `${secs}s`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
      </div>
    )
  }

  if (!userStats) {
    return (
      <div className="flex items-center justify-center min-h-[200px] text-white/60">
        No study data available
      </div>
    )
  }

  // Get today's data
  const today = new Date().toISOString().split('T')[0]
  const todayData = userStats.dailyProgress.find(day => day.date === today)

  // Prepare subject overviews for today
  const todaySubjectOverviews = todayData && todayData.subjectDurations
    ? Object.entries(todayData.subjectDurations)
      .filter(([_, duration]) => duration > 0)
      .map(([subject, duration], index) => ({
        subject,
        duration,
        percentageOfDay: (duration / todayData.totalDuration) * 100,
        color: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'][index % 5]
      }))
      .sort((a, b) => b.duration - a.duration)
    : []

  return (
    <div className="space-y-6">
      {/* First row - Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white/10 border-white/20">
          <CardHeader className="py-3">
            <CardTitle className="text-sm">AVG FOCUS/DAY</CardTitle>
          </CardHeader>
          <CardContent className="py-2">
            <div className="text-xl font-bold text-purple-400">
              {formatDuration(userStats.avgFocusPerDay)}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 border-white/20">
          <CardHeader className="py-3">
            <CardTitle className="text-sm">Completed Pomodoros</CardTitle>
          </CardHeader>
          <CardContent className="py-2">
            <div className="text-xl font-bold text-purple-400">
              {userStats.completedPomodoros}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 border-white/20">
          <CardHeader className="py-3">
            <CardTitle className="text-sm">Subjects Studied Today</CardTitle>
          </CardHeader>
          <CardContent className="py-2">
            <div className="text-xl font-bold text-purple-400">
              {userStats.todaySubjects}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 border-white/20">
          <CardHeader className="py-3">
            <CardTitle className="text-sm">Study Streak</CardTitle>
          </CardHeader>
          <CardContent className="py-2">
            <div className="text-xl font-bold text-purple-400">
              {userStats.studyStreak} days
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second row - Today's focus */}
      {todayData && (
        <Card className="bg-white/10 border-white/20">
          <CardHeader className="py-3 flex flex-row items-center justify-between">
            <CardTitle className="text-base md:text-lg">Total Focus (Today)</CardTitle>
            <div className={`text-base font-bold flex items-center ${userStats.todayFocusPercentage >= 50 ? 'text-green-400' : 'text-red-400'}`}>
              {userStats.todayFocusPercentage >= 50 ? (
                <ArrowUp className="h-4 w-4 mr-1" />
              ) : (
                <ArrowDown className="h-4 w-4 mr-1" />
              )}
              {userStats.todayFocusPercentage.toFixed(0)}%
            </div>
          </CardHeader>
          <CardContent className="py-2">
            <div className="text-xl md:text-2xl font-medium text-purple-400">
              {formatDuration(todayData.totalDuration)}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Third row - Daily Subject Overview */}
      {todaySubjectOverviews.length > 0 && (
        <Card className="bg-white/10 border-white/20">
          <CardHeader className="py-3">
            <CardTitle>Daily Subject Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                <h3 className="text-sm font-medium text-white/80 mb-1">Total Study Time</h3>
                <p className="text-2xl font-bold text-purple-400">{formatDuration(todayData.totalDuration)}</p>
              </div>
              <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                <h3 className="text-sm font-medium text-white/80 mb-1">Completed Pomodoros</h3>
                <p className="text-2xl font-bold text-pink-400">{todayData.completedPomodoros}</p>
              </div>
              <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                <h3 className="text-sm font-medium text-white/80 mb-1">Subjects Studied</h3>
                <p className="text-2xl font-bold text-cyan-400">{todaySubjectOverviews.length}</p>
              </div>
            </div>

            {/* Subjects list in separate rows */}
            <div className="grid grid-cols-1 gap-2">
              {todaySubjectOverviews.map((subject) => (
                <div key={subject.subject} className="flex items-center p-3 bg-white/5 rounded-md border border-white/10">
                  <div
                    className="w-3 h-3 rounded-full mr-3"
                    style={{ backgroundColor: subject.color }}
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-white">{subject.subject}</h4>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-white/80">{formatDuration(subject.duration)}</p>
                    <p className="text-xs text-white/60">{subject.percentageOfDay.toFixed(1)}% of day</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}