import {
  Di<PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { UserAnalytics } from "./UserAnalytics"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User } from "lucide-react"

interface UserProfileDialogProps {
  userId: string
  username: string
  photoURL?: string
  children: React.ReactNode
}

export function UserProfileDialog({ userId, username, photoURL, children }: UserProfileDialogProps) {

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto bg-background border-border dark:bg-gradient-to-br dark:from-slate-800/90 dark:to-slate-900/90 dark:border-white/10 backdrop-blur-xl shadow-2xl">
        <DialogHeader className="flex flex-row items-center gap-4 mb-6 border-b border-border dark:border-white/10 pb-4">
          <Avatar className="h-16 w-16 border-2 border-border dark:border-white/10">
            <AvatarImage src={photoURL} />
            <AvatarFallback className="bg-muted dark:bg-white/5">
              <User className="h-8 w-8 text-muted-foreground dark:text-white/60" />
            </AvatarFallback>
          </Avatar>
          <div>
            <DialogTitle className="text-2xl text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-indigo-400 dark:to-pink-400 font-bold">
              {username}'s Study Analytics
            </DialogTitle>
            <p className="text-muted-foreground dark:text-white/60 mt-1">Detailed progress and study patterns</p>
          </div>
        </DialogHeader>
        <UserAnalytics userId={userId} />
      </DialogContent>
    </Dialog>
  )
}