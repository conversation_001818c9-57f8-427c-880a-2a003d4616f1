import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import { useChatStore } from '@/stores/chatStore'
import { useToast } from '@/components/ui/use-toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Globe, Lock, Users } from 'lucide-react'
import { getPublicGroups, getGroupByInviteCode, joinGroup } from '@/utils/supabase'

interface JoinGroupProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onJoinSuccess?: (groupId: string) => void
}

interface PublicGroup {
  id: string
  name: string
  memberCount: number
  ownerId: string
}

export function Join<PERSON>roup({ open, onOpenChange, onJoinSuccess }: JoinGroupProps) {
  const [inviteCode, setInviteCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [publicGroups, setPublicGroups] = useState<PublicGroup[]>([])
  const [isLoadingPublicGroups, setIsLoadingPublicGroups] = useState(false)
  const [joiningGroupId, setJoiningGroupId] = useState<string | null>(null)
  const { user } = useSupabaseAuth()
  const { groups, addGroup } = useChatStore()
  const { toast } = useToast()

  // Fetch public groups when dialog opens
  useEffect(() => {
    if (open) {
      fetchPublicGroups()
    }
  }, [open])

  const fetchPublicGroups = async () => {
    if (!user) return

    setIsLoadingPublicGroups(true)
    try {
      // Get all public groups excluding ones user is already in
      const publicGroupsData = await getPublicGroups(user.id)

      // Filter out groups the user is already a member of
      const userGroupIds = groups.map(g => g.id)
      const availableGroups = publicGroupsData
        .filter(group => !userGroupIds.includes(group.id))
        .map(group => ({
          id: group.id,
          name: group.name,
          memberCount: (group.members || []).length,
          ownerId: group.owner_id || group.createdBy
        }))

      setPublicGroups(availableGroups)
    } catch (error) {
      console.error('Error fetching public groups:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load public groups"
      })
    } finally {
      setIsLoadingPublicGroups(false)
    }
  }

  const handleJoin = async () => {
    if (!user || !inviteCode.trim()) return

    setIsLoading(true)
    try {
      // Find group with matching invite code
      const groupData = await getGroupByInviteCode(inviteCode.toUpperCase())

      if (!groupData) {
        toast({
          variant: "destructive",
          title: "Invalid Code",
          description: "No group found with this invite code"
        })
        return
      }

      // Check if user is already a member
      const members = groupData.members || []
      if (members.includes(user.id)) {
        toast({
          variant: "destructive",
          title: "Already Joined",
          description: "You are already a member of this group"
        })
        return
      }

      // Join the group using Supabase function
      const updatedGroup = await joinGroup(groupData.id, user.id)

      // Add group to local state
      const chatGroup = {
        id: updatedGroup.id,
        name: updatedGroup.name,
        description: updatedGroup.description || '',
        members: updatedGroup.members || [],
        createdAt: updatedGroup.createdAt,
        createdBy: updatedGroup.createdBy,
        owner_id: updatedGroup.owner_id,
        inviteCode: updatedGroup.inviteCode || '',
        isPublic: updatedGroup.isPublic || false
      }

      addGroup(chatGroup)

      toast({
        title: "Success",
        description: "You have joined the group"
      })

      onOpenChange(false)
      setInviteCode('')
      if (onJoinSuccess) {
        onJoinSuccess(updatedGroup.id)
      }
    } catch (error) {
      console.error('Error joining group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to join group. Please try again."
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleJoinPublicGroup = async (groupId: string) => {
    if (!user) return

    setJoiningGroupId(groupId)
    try {
      // Join the group using Supabase function
      const updatedGroup = await joinGroup(groupId, user.id)

      // Add group to local state
      const chatGroup = {
        id: updatedGroup.id,
        name: updatedGroup.name,
        description: updatedGroup.description || '',
        members: updatedGroup.members || [],
        createdAt: updatedGroup.createdAt,
        createdBy: updatedGroup.createdBy,
        owner_id: updatedGroup.owner_id,
        inviteCode: updatedGroup.inviteCode || '',
        isPublic: updatedGroup.isPublic || false
      }

      addGroup(chatGroup)

      // Remove from public groups list
      setPublicGroups(publicGroups.filter(g => g.id !== groupId))

      toast({
        title: "Success",
        description: "You have joined the group"
      })

      // Call the success callback if provided
      if (onJoinSuccess) {
        onJoinSuccess(groupId)
        // Close the dialog
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error joining public group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to join group. Please try again."
      })
    } finally {
      setJoiningGroupId(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {/* Theme-aware Dialog Content */}
      <DialogContent className="bg-background border-border text-foreground sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Join Group</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Join a study group to collaborate with others
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="public" className="w-full">
          {/* Theme-aware TabsList */}
          <TabsList className="grid grid-cols-2 mb-4 bg-muted border-border">
            {/* Theme-aware TabsTrigger */}
            <TabsTrigger value="public" className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground">
              <Globe className="h-4 w-4" />
              <span>Public Groups</span>
            </TabsTrigger>
            {/* Theme-aware TabsTrigger */}
            <TabsTrigger value="private" className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground">
              <Lock className="h-4 w-4" />
              <span>Private Groups</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="public" className="space-y-4">
            {isLoadingPublicGroups ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
              </div>
            ) : publicGroups.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No public groups available</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                {publicGroups.map(group => (
                  // Theme-aware list item
                  <div 
                    key={group.id}
                    className="flex items-center justify-between p-3 bg-muted dark:bg-white/5 hover:bg-accent dark:hover:bg-white/10 rounded-lg border border-border dark:border-white/10 transition-colors"
                  >
                    <div>
                      {/* Theme-aware text */}
                      <h3 className="font-medium text-foreground">{group.name}</h3>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Users className="h-3 w-3" />
                        <span>{group.memberCount} members</span>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleJoinPublicGroup(group.id)}
                      disabled={joiningGroupId === group.id}
                    >
                      {joiningGroupId === group.id ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
                          <span>Joining...</span>
                        </div>
                      ) : (
                        "Join"
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="private" className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="code">Invite Code</Label>
              {/* Theme-aware Input */}
              <Input
                id="code"
                value={inviteCode}
                onChange={(e) => setInviteCode(e.target.value)}
                placeholder="Enter invite code"
                maxLength={6}
                className="uppercase bg-muted border-border"
              />
              <p className="text-xs text-muted-foreground">
                Enter the 6-character invite code shared by the group owner
              </p>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="invite-link">Or Join via Link</Label>
              {/* Theme-aware Input */}
              <Input
                id="invite-link"
                placeholder="Paste invite link here"
                className="bg-muted border-border"
                onChange={(e) => {
                  // Extract invite code from link if possible
                  try {
                    const url = new URL(e.target.value);
                    const code = url.searchParams.get('code');
                    if (code) {
                      setInviteCode(code.toUpperCase());
                    }
                  } catch (error) {
                    // Not a valid URL, ignore
                  }
                }}
              />
              <p className="text-xs text-muted-foreground">
                Paste the full invite link shared with you
              </p>
            </div>
            
            {/* Theme-aware info box */}
            <div className="bg-muted p-3 rounded-md mt-4 border border-border">
              <h4 className="font-medium mb-1 text-foreground">How to Join</h4>
              <p className="text-xs text-muted-foreground">
                You can join a group by entering the 6-character invite code or by pasting the full invite link.
                If someone shared a link with you, you can also click it directly to join the group automatically.
              </p>
            </div>
            
            <Button
              onClick={handleJoin}
              disabled={!inviteCode.trim() || isLoading}
              className="w-full"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                  <span>Joining...</span>
                </div>
              ) : (
                "Join Private Group"
              )}
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
