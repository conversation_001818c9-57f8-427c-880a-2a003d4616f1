import { ReactNode } from 'react';

interface PageTitleProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
}

export function PageTitle({ title, subtitle, icon }: PageTitleProps) {
  return (
    <div className="flex items-center gap-3">
      {icon && (
        <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-primary/10 text-primary rounded-lg">
          {icon}
        </div>
      )}
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </div>
    </div>
  );
} 