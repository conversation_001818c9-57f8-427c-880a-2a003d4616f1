import { ReactNode } from 'react';
import { AdPlaceholder } from './AdPlaceholder';

interface AdLayoutProps {
  children: ReactNode;
  showTopAd?: boolean;
  showSideAd?: boolean;
  showBottomAd?: boolean;
  className?: string;
}

/**
 * AdLayout component for wrapping page content with properly positioned ads
 * 
 * This component provides a consistent layout for pages with ads, positioning
 * them in appropriate locations (top, side, bottom) according to best practices.
 * 
 * @param children - The page content to display
 * @param showTopAd - Whether to show an ad at the top of the page
 * @param showSideAd - Whether to show an ad on the side of the page
 * @param showBottomAd - Whether to show an ad at the bottom of the page
 * @param className - Additional CSS classes to apply to the container
 */
export const AdLayout = ({
  children,
  showTopAd = true,
  showSideAd = true,
  showBottomAd = true,
  className = ''
}: AdLayoutProps) => {
  return (
    <div className={`container mx-auto px-4 ${className}`}>
      {/* Top ad - horizontal banner */}
      {showTopAd && (
        <div className="w-full my-4 flex justify-center">
          <AdPlaceholder 
            format="horizontal" 
            className="max-w-full" 
            slot="top-banner"
          />
        </div>
      )}
      
      {/* Main content with optional side ad */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Main content */}
        <div className="flex-1">
          {children}
        </div>
        
        {/* Side ad - vertical banner, only on desktop */}
        {showSideAd && (
          <div className="hidden lg:block w-[300px] shrink-0">
            <div className="sticky top-24">
              <AdPlaceholder 
                format="rectangle" 
                className="w-full" 
                slot="side-rectangle"
              />
            </div>
          </div>
        )}
      </div>
      
      {/* Bottom ad - horizontal banner */}
      {showBottomAd && (
        <div className="w-full my-8 flex justify-center">
          <AdPlaceholder 
            format="horizontal" 
            className="max-w-full" 
            slot="bottom-banner"
          />
        </div>
      )}
    </div>
  );
};
