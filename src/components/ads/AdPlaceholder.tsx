import { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface AdPlaceholderProps {
  className?: string;
  slot?: string;
  format?: 'auto' | 'horizontal' | 'vertical' | 'rectangle';
  responsive?: boolean;
  testMode?: boolean;
}

/**
 * AdPlaceholder component for displaying Google AdSense advertisements
 * 
 * This component creates a container for Google AdSense ads and handles
 * the initialization of ads when the component mounts.
 * 
 * @param className - Additional CSS classes to apply to the ad container
 * @param slot - The AdSense ad slot ID (required for production)
 * @param format - The ad format (auto, horizontal, vertical, rectangle)
 * @param responsive - Whether the ad should be responsive
 * @param testMode - Whether to display a test ad placeholder instead of a real ad
 */
export const AdPlaceholder = ({
  className,
  slot = '1234567890', // Replace with your actual ad slot in production
  format = 'auto',
  responsive = true,
  testMode = process.env.NODE_ENV !== 'production'
}: AdPlaceholderProps) => {
  const adRef = useRef<HTMLDivElement>(null);
  const [adLoaded, setAdLoaded] = useState(false);
  
  useEffect(() => {
    // Only attempt to load ads in client-side environment
    if (typeof window === 'undefined') return;
    
    // Skip actual ad loading in test mode
    if (testMode) {
      setAdLoaded(true);
      return;
    }
    
    try {
      // Check if AdSense script is loaded
      if (window.adsbygoogle) {
        // Push the ad to AdSense for rendering
        (window.adsbygoogle = window.adsbygoogle || []).push({});
        setAdLoaded(true);
      } else {
        console.warn('AdSense not loaded yet');
      }
    } catch (error) {
      console.error('Error loading AdSense ad:', error);
    }
  }, [testMode]);
  
  // Format-specific classes
  const formatClasses = {
    auto: 'min-h-[90px]',
    horizontal: 'min-h-[90px] min-w-[728px]',
    vertical: 'min-h-[600px] min-w-[120px]',
    rectangle: 'min-h-[250px] min-w-[300px]',
  };
  
  // If in test mode, show a placeholder
  if (testMode) {
    return (
      <div 
        className={cn(
          'bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 border border-gray-300 dark:border-gray-700 rounded-md flex items-center justify-center text-center p-4',
          formatClasses[format],
          className
        )}
      >
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Advertisement Placeholder</p>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
            {format} format {responsive ? '(responsive)' : ''}
          </p>
        </div>
      </div>
    );
  }
  
  // Real ad implementation
  return (
    <div className={cn('overflow-hidden', className)}>
      <ins
        ref={adRef}
        className="adsbygoogle"
        style={{ display: 'block', textAlign: 'center' }}
        data-ad-client="ca-pub-9602732057654649" // Your AdSense Publisher ID
        data-ad-slot={slot}
        data-ad-format={format}
        data-full-width-responsive={responsive ? 'true' : 'false'}
      />
      {!adLoaded && (
        <div className="text-xs text-gray-400 dark:text-gray-600 text-center mt-1">
          Loading advertisement...
        </div>
      )}
    </div>
  );
};

// Add this to the global Window interface
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}
