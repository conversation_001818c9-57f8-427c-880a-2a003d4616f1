import React from "react";
import ReactMarkdown from "react-markdown";
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

import 'katex/dist/katex.min.css';
import { Copy, Check, MessageSquare, User, Link2, Image as ImageIcon, Bot } from "lucide-react";
import { useToast } from "./ui/use-toast";
import { Button } from "./ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { DesmosGraph } from "./DesmosGraph";
import { motion } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { cn } from "@/lib/utils";

interface ChatMessageProps {
  content: string;
  isUser: boolean;
  image?: {
    url: string;
    name: string;
    urlKey?: string;
    publicId?: string;
  };
  hideCodeBlocks?: boolean;
}

interface ToolOutput {
  type: 'desmos';
  data: {
    expression: string;
  };
}

const isToolOutput = (content: string): ToolOutput | null => {
  try {
    if (content.startsWith('```tool-output')) {
      const jsonStr = content.replace('```tool-output\n', '').replace('\n```', '');
      const data = JSON.parse(jsonStr);
      if (data.type && data.data) {
        return data as ToolOutput;
      }
    }
    return null;
  } catch (error) {
    return null;
  }
};

// Helper function to detect and format math notations in different formats
const formatMathNotation = (content: string): string => {
  if (!content || typeof content !== 'string') return '';

  try {
    let processed = content;

    // Convert \( \) notation to $ $ for inline math
    processed = processed.replace(/\\\((.*?)\\\)/g, '$$$1$$');

    // Convert \[ \] notation to $$ $$ for display math
    processed = processed.replace(/\\\[(.*?)\\\)/g, '$$$$1$$');

    // Handle <math> tags
    processed = processed.replace(/<math>(.*?)<\/math>/g, '$$$1$$');

    // Handle <math display="block"> tags
    processed = processed.replace(/<math\s+display="block">(.*?)<\/math>/g, '$$$$1$$');

    // Handle Gemini's sometimes unusual double backtick format for math
    processed = processed.replace(/``([^`]+?)``/g, '$$$1$$');

    // Detect and format single dollar sign math notation
    // This regex looks for text between single dollar signs that contains at least one math character
    // Math characters include: +, -, *, /, =, <, >, (, ), [, ], {, }, ^, _, \, letters, numbers
    const singleDollarRegex = /\$((?![\s$])(?:[a-zA-Z0-9+\-*\/=<>()\[\]{}^_\\])+(?<![\s$]))\$/g;
    processed = processed.replace(singleDollarRegex, (match, equation) => {
      // Make sure we're not replacing currency symbols
      // If the equation is just a number, it's likely a currency symbol
      if (/^\d+(\.\d+)?$/.test(equation)) {
        return match; // Return the original match (likely a currency symbol)
      }
      return '$' + equation + '$';
    });

    // Fix cases where dollars might be too close to text
    processed = processed.replace(/([a-zA-Z0-9])(\$\$?)/g, '$1 $2');
    processed = processed.replace(/(\$\$?)([a-zA-Z0-9])/g, '$1 $2');

    // Replace display math with custom HTML tag
    // This prevents Markdown from interpreting the equations as code blocks
    processed = processed.replace(/\$\$(.*?)\$\$/g, (match) => {
      // Extract the equation content without the $$ delimiters
      const equation = match.slice(2, -2).trim();
      // Wrap it in our custom tag with proper spacing
      return '\n<display-math>$' + equation + '$</display-math>\n';
    });

    return processed;
  } catch (error) {
    console.error('Error formatting math notation:', error);
    return content;
  }
};

export const ChatMessage = ({ content, isUser, image, hideCodeBlocks = false }: ChatMessageProps) => {
  const [isCopied, setIsCopied] = React.useState(false);
  const { toast } = useToast();
  const toolOutput = !isUser ? isToolOutput(content) : null;
  const username = localStorage.getItem("userName") || "User";
  const userPhotoUrl = localStorage.getItem("userPhotoUrl");

  // We don't need to filter code blocks anymore, just keep the content

  const markdownComponents = React.useMemo(() => ({
    // Custom component for display-math tag
    'display-math': ({ node, ...props }) => {
      // The content will be passed as children, but we need to ensure it's treated as display math
      return (
        <div className="math-display-container flex justify-center w-full my-4">
          <div className="inline-block katex-display-wrapper">
            {/* Add $$ to make it display math */}
            {props.children}
          </div>
        </div>
      );
    },
    h1: (props: React.HTMLProps<HTMLHeadingElement>) => (
      <h1 className="text-3xl font-bold mt-8 mb-5 font-spaceGrotesk" {...props} />
    ),
    h2: (props: React.HTMLProps<HTMLHeadingElement>) => (
      <h2 className="text-2xl font-bold mt-7 mb-4 font-spaceGrotesk" {...props} />
    ),
    h3: (props: React.HTMLProps<HTMLHeadingElement>) => (
      <h3 className="text-xl font-semibold mt-6 mb-3 font-spaceGrotesk" {...props} />
    ),
    pre: ({ node, ...props }) => {
      // Convert code blocks to paragraphs if hideCodeBlocks is true and it's an AI message
      if (hideCodeBlocks && !isUser) {
        return (
          <p {...props} className="whitespace-pre-wrap break-words my-3" />
        );
      }
      return (
        <pre {...props} className="whitespace-pre-wrap break-words p-4 rounded-lg bg-secondary/15 backdrop-blur-sm my-4 overflow-x-auto border border-border/40 shadow-sm text-sm" />
      );
    },
    code: ({ node, ...props }) => {
      // Convert inline code to regular text if hideCodeBlocks is true and it's an AI message
      if (hideCodeBlocks && !isUser) {
        return (
          <span {...props} className="whitespace-pre-wrap break-words" />
        );
      }
      return (
        <code {...props} className="whitespace-pre-wrap break-words py-0.5 px-1.5 rounded bg-secondary/20 font-mono text-sm" />
      );
    },
    a: ({ node, ...props }) => (
      <a
        {...props}
        target="_blank"
        rel="noopener noreferrer"
        className="text-primary hover:underline transition-all duration-200 font-medium inline-flex items-center gap-1 after:content-['_↗']"
      />
    ),
    ul: ({node, ...props}) => (
      <ul className="list-disc list-outside pl-6 my-4 space-y-2" {...props} />
    ),
    ol: ({node, ...props}) => (
      <ol className="list-decimal list-outside pl-6 my-4 space-y-2" {...props} />
    ),
    li: ({node, ...props}) => (
      <li className="my-2 marker:text-primary/70" {...props} />
    ),
    blockquote: ({node, ...props}) => (
      <blockquote className="border-l-4 border-primary/30 pl-4 py-2 my-5 italic bg-primary/5 rounded-r-lg" {...props} />
    ),
    table: ({node, ...props}) => (
      <div className="overflow-x-auto my-5 rounded-lg border border-border/50 shadow-sm">
        <table className="min-w-full divide-y divide-border/30" {...props} />
      </div>
    ),
    th: ({node, ...props}) => (
      <th className="px-4 py-3 bg-secondary/20 text-left text-xs font-medium uppercase tracking-wider" {...props} />
    ),
    td: ({node, ...props}) => (
      <td className="px-4 py-3 whitespace-nowrap text-sm border-t border-border/20" {...props} />
    ),
    img: ({node, ...props}) => (
      <div className="my-5 rounded-lg overflow-hidden border border-border/30 shadow-md transition-all duration-300 hover:shadow-lg">
        <img {...props} className="max-w-full h-auto" style={{ maxHeight: '500px', objectFit: 'contain' }} />
      </div>
    ),
    p: ({node, children, ...props}) => {
      // Only apply this to AI messages (not user messages)
      if (!isUser) {
        if (typeof children === 'string') {
          // Skip processing if already contains math elements or appears to be handled
          if (children.includes('class="math"') || children.includes('\\(') || children.includes('\\)')) {
            return <p {...props}>{children}</p>;
          }
          
          // Skip if double dollar signs present to avoid conflicts
          if (children.includes('$$')) {
            return <p {...props}>{children}</p>;
          }
          
          // First, preprocess to normalize spaces inside dollar signs
          // This will help with cases like "$ f(x)$" or "$x $"
          let processedText = children;
          
          // Process single dollars with an improved pattern that handles spacing better
          // First find all potential math expressions between single dollar signs
          const mathPattern = /\$(.*?)\$/g;
          processedText = processedText.replace(mathPattern, (match, expr) => {
            // Skip if it's just a number (likely currency)
            if (/^\s*\d+(\.\d+)?\s*$/.test(expr)) {
              return match;
            }
            
            // Check if the content between dollars contains any math notation
            // This helps avoid converting things like "$5" (currency)
            const hasMathSymbols = /[a-zA-Z\+\-\*\/\=\(\)\[\]\{\}\^\_\\]/.test(expr);
            if (!hasMathSymbols) {
              return match; // Not math notation, return unchanged
            }
            
            // Trim spaces from the expression but keep spaces needed for readability inside expressions
            const trimmedExpr = expr.trim();
            return `\\(${trimmedExpr}\\)`;
          });
          
          return <p {...props} dangerouslySetInnerHTML={{ __html: processedText }} />;
        } else if (Array.isArray(children)) {
          // Process each child that might be a string
          const processedChildren = React.Children.map(children, child => {
            if (typeof child === 'string') {
              // Skip if already math or contains double dollars
              if (child.includes('class="math"') || child.includes('\\(') || child.includes('\\)') || child.includes('$$')) {
                return child;
              }
              
              // Process with the same pattern as above
              let processedText = child;
              const mathPattern = /\$(.*?)\$/g;
              processedText = processedText.replace(mathPattern, (match, expr) => {
                // Skip if it's just a number (likely currency)
                if (/^\s*\d+(\.\d+)?\s*$/.test(expr)) {
                  return match;
                }
                
                // Check for math symbols
                const hasMathSymbols = /[a-zA-Z\+\-\*\/\=\(\)\[\]\{\}\^\_\\]/.test(expr);
                if (!hasMathSymbols) {
                  return match;
                }
                
                // Trim and format
                const trimmedExpr = expr.trim();
                return `\\(${trimmedExpr}\\)`;
              });
              
              return <span dangerouslySetInnerHTML={{ __html: processedText }} />;
            }
            return child;
          });
          
          return <p {...props}>{processedChildren}</p>;
        }
      }
      
      return <p className="leading-7 mb-4" {...props}>{children}</p>;
    },
  }), [isUser]);

  const handleCopy = async () => {
    if (content) {
      try {
        await navigator.clipboard.writeText(content);
        setIsCopied(true);
        toast({
          title: "Copied!",
          description: "Message content copied to clipboard",
        });
        setTimeout(() => setIsCopied(false), 2000);
      } catch (error) {
        console.error("Failed to copy:", error);
        toast({
          title: "Copy failed",
          description: "Could not copy to clipboard. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const renderToolOutput = (output: ToolOutput) => {
    if (output.type === 'desmos') {
      return <DesmosGraph expression={output.data.expression} />;
    }
    return null;
  };

  // Modern helper to render the avatar icons
  const renderAvatar = () => {
    if (isUser) {
      return (
        <Avatar className="h-8 w-8 border-2 border-primary/20">
          {userPhotoUrl ? (
            <AvatarImage src={userPhotoUrl} alt={username} />
          ) : (
            <AvatarFallback className="bg-primary/10 text-primary">
              <User className="h-4 w-4" />
            </AvatarFallback>
          )}
        </Avatar>
      );
    } else {
      return (
        <Avatar className="h-8 w-8 border-2 border-primary/20">
          <AvatarFallback className="bg-primary/10 text-primary">
            <Bot className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>
      );
    }
  };

  return (
    <div className={cn(
      "group flex items-start gap-3",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar */}
      {renderAvatar()}
      
      {/* Message content */}
      <div className={cn(
        "flex-1 relative max-w-3xl",
        isUser ? "mr-0 ml-auto" : "ml-0 mr-auto"
      )}>
        {/* Actual message bubble with modern styling */}
        <div className={cn(
          "px-4 py-3 rounded-2xl shadow-sm",
          isUser 
            ? "bg-primary text-primary-foreground rounded-tr-none" 
            : "bg-card border border-border/50 backdrop-blur-sm rounded-tl-none"
        )}>
          {toolOutput ? (
            renderToolOutput(toolOutput)
          ) : (
            <>
              {image && (
                <div className="mb-4 rounded-lg overflow-hidden border border-border/30 shadow-md">
                  <a href={image.url} target="_blank" rel="noopener noreferrer" className="block relative">
                    <div className="absolute top-2 right-2 bg-black/60 rounded-full p-1.5 backdrop-blur-sm">
                      <ImageIcon className="h-3.5 w-3.5 text-white/90" />
                    </div>
                    <img
                      src={image.url}
                      alt={image.name}
                      className="max-w-full h-auto rounded-lg"
                      style={{ maxHeight: '400px', objectFit: 'contain' }}
                    />
                  </a>
                </div>
              )}
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeKatex, rehypeRaw]}
                components={markdownComponents}
                className={cn(
                  "prose prose-sm max-w-none break-words",
                  isUser ? "prose-invert" : "prose-neutral"
                )}
              >
                {formatMathNotation(content)}
              </ReactMarkdown>
            </>
          )}
        </div>
        
        {/* Action buttons */}
        <div className={cn(
          "absolute top-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",
          isUser ? "left-3" : "right-3"
        )}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleCopy}
                  className="h-6 w-6 rounded-full bg-background/80 backdrop-blur-sm shadow-sm hover:bg-background"
                >
                  {isCopied ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3 text-muted-foreground" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>Copy message</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};
