import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  getUserSubjects,
  createUserSubject,
  updateUserSubject,
  deleteUserSubject,
  subscribeToUserSubjects,
} from '../utils/supabase';
import { Database } from '../integrations/supabase/types';
import { v4 as uuidv4 } from 'uuid';

// Type definitions
type UserSubjectRow = Database['public']['Tables']['userSubjects']['Row'];

interface Subject {
  id: string;
  name: string;
  color: string;
  userId: string;
  createdAt: string;
}

interface SubjectState {
  subjects: Subject[];
  isLoading: boolean;
  lastFetched: number | null;
  error: string | null;
  setSubjects: (subjects: Subject[]) => void;
  setLoading: (loading: boolean) => void;
  setLastFetched: (timestamp: number | null) => void;
  setError: (error: string | null) => void;
  fetchSubjects: (userId: string, forceRefresh?: boolean) => Promise<Subject[]>;
  addSubject: (userId: string, name: string, color: string) => Promise<Subject>;
  updateSubject: (subjectId: string, updates: Partial<Subject>) => Promise<Subject>;
  deleteSubject: (subjectId: string) => Promise<void>;
  reset: () => void;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const initialState = {
  subjects: [],
  isLoading: true,
  lastFetched: null,
  error: null,
};

// Convert Supabase UserSubjectRow to Subject
const convertToSubject = (row: UserSubjectRow): Subject => ({
  id: row.id,
  name: row.name,
  color: row.color || '#3B82F6',
  userId: row.userId,
  createdAt: row.createdAt || new Date().toISOString(),
});

export const useSupabaseSubjectStore = create<SubjectState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setSubjects: (subjects) => {
        console.log('Setting subjects in store:', subjects.length);
        set({ 
          subjects, 
          isLoading: false, 
          lastFetched: Date.now(),
          error: null 
        });
      },
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setLastFetched: (timestamp) => set({ lastFetched: timestamp }),
      
      setError: (error) => set({ error }),
      
      fetchSubjects: async (userId, forceRefresh = false) => {
        const { subjects, lastFetched } = get();
        
        // Return cached data if available and not forcing refresh
        if (!forceRefresh && subjects.length > 0 && lastFetched && (Date.now() - lastFetched < CACHE_DURATION)) {
          console.log('Returning cached subjects for user:', userId);
          return subjects;
        }

        set({ isLoading: true, error: null });
        
        try {
          console.log('Fetching subjects from Supabase for user:', userId);
          const userSubjects = await getUserSubjects(userId);
          const subjects = userSubjects.map(convertToSubject);
          
          get().setSubjects(subjects);
          
          // Set up real-time subscription
          const subscription = subscribeToUserSubjects(userId, (payload) => {
            console.log('Real-time subject update:', payload);
            // Refetch subjects when changes occur
            get().fetchSubjects(userId, true);
          });

          // Store subscription for cleanup
          (get() as any).subscription = subscription;
          
          return subjects;
        } catch (error: any) {
          console.error('Error fetching subjects:', error);
          set({ error: error.message, isLoading: false });
          return [];
        }
      },
      
      addSubject: async (userId, name, color) => {
        try {
          const subjectId = uuidv4();
          const subjectData = {
            id: subjectId,
            userId,
            name,
            color,
            createdAt: new Date().toISOString(),
          };

          console.log('Adding subject to Supabase:', subjectData);
          const createdSubject = await createUserSubject(subjectData);
          const subject = convertToSubject(createdSubject);
          
          // Update local state
          const currentSubjects = get().subjects;
          set({ 
            subjects: [...currentSubjects, subject],
            lastFetched: Date.now()
          });
          
          return subject;
        } catch (error: any) {
          console.error('Error adding subject:', error);
          set({ error: error.message });
          throw error;
        }
      },
      
      updateSubject: async (subjectId, updates) => {
        try {
          console.log('Updating subject in Supabase:', subjectId, updates);
          const updatedSubject = await updateUserSubject(subjectId, updates);
          const subject = convertToSubject(updatedSubject);
          
          // Update local state
          const currentSubjects = get().subjects;
          const updatedSubjects = currentSubjects.map(s => 
            s.id === subjectId ? subject : s
          );
          
          set({ 
            subjects: updatedSubjects,
            lastFetched: Date.now()
          });
          
          return subject;
        } catch (error: any) {
          console.error('Error updating subject:', error);
          set({ error: error.message });
          throw error;
        }
      },
      
      deleteSubject: async (subjectId) => {
        try {
          console.log('Deleting subject from Supabase:', subjectId);
          await deleteUserSubject(subjectId);
          
          // Update local state
          const currentSubjects = get().subjects;
          const filteredSubjects = currentSubjects.filter(s => s.id !== subjectId);
          
          set({ 
            subjects: filteredSubjects,
            lastFetched: Date.now()
          });
        } catch (error: any) {
          console.error('Error deleting subject:', error);
          set({ error: error.message });
          throw error;
        }
      },
      
      reset: () => {
        console.log('Resetting subject store');
        
        // Clean up subscription
        const subscription = (get() as any).subscription;
        if (subscription) {
          subscription.unsubscribe();
        }
        
        set(initialState);
      },
    }),
    {
      name: 'supabase-subject-storage',
      // Persist subjects and metadata
      partialize: (state) => ({
        subjects: state.subjects,
        lastFetched: state.lastFetched,
      }),
    }
  )
);
