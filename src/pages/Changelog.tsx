import { Changelog } from "@/components/Changelog";
import { Helmet } from "react-helmet";
import { Link } from "react-router-dom";
import { FeedbackWidget } from '@/components/FeedbackWidget';
import { useEffect } from 'react';

const changelogData = [
  {
    date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
    changes: [
      {
        title: "Enhanced Picture-in-Picture Timer Stability",
        description: "Improved the reliability and stability of the Picture-in-Picture timer feature:",
        items: [
          "Fixed issue with PiP window closing unexpectedly during timer operation",
          "Added continuous animation to maintain active video stream",
          "Increased frame rate and update frequency for smoother display",
          "Implemented more robust keep-alive mechanisms",
          "Added automatic recovery for video playback interruptions",
          "Enhanced error handling and debugging capabilities",
          "Improved cross-browser compatibility"
        ]
      }
    ]
  },
  {
    date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
    changes: [
      {
        title: "Added Picture-in-Picture Timer Mode",
        description: "Implemented a Picture-in-Picture feature for the productivity timer:",
        items: [
          "Added ability to view the timer in a floating window while using other tabs or applications",
          "Created a dedicated PiP button in the timer interface",
          "Implemented real-time timer updates in the PiP window",
          "Displayed current subject and phase information in the PiP view",
          "Added browser compatibility detection for PiP support",
          "Ensured the timer continues to function accurately in PiP mode"
        ]
      }
    ]
  },
  {
    date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
    changes: [
      {
        title: "Fixed Timer Second Loss Issue",
        description: "Resolved the issue with second loss during minute transitions in the productivity timer:",
        items: [
          "Fixed the bug where a second would be lost when transitioning from 00:59 to 01:00",
          "Improved time calculation precision to ensure accurate second tracking",
          "Implemented high-precision time measurement using Performance API",
          "Reduced timer interval for more accurate second transitions",
          "Added comprehensive error handling for timer operations",
          "Enhanced debugging capabilities to track time precision issues"
        ]
      }
    ]
  },
  {
    date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
    changes: [
      {
        title: "Advanced Time Tracking System",
        description: "Completely redesigned the productivity timer with specialized libraries for accurate time tracking:",
        items: [
          "Implemented worker-timers library for accurate background time tracking",
          "Added accurate-interval library for precise timer intervals",
          "Fixed issues with unrealistic time values in the timer display",
          "Improved background time tracking when tab is inactive or minimized",
          "Added detection and handling for device sleep/wake cycles",
          "Implemented safeguards against system time changes",
          "Enhanced synchronization between the timer and the UI using Performance API",
          "Added comprehensive validation to prevent storing invalid study session durations"
        ]
      }
    ]
  },
  {
    date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
    changes: [
      {
        title: "Improved Time Tracking System",
        description: "Enhanced the productivity timer with a more robust time tracking mechanism:",
        items: [
          "Fixed issues with unrealistic time values in the timer display",
          "Improved background time tracking when tab is inactive or minimized",
          "Added detection and handling for device sleep/wake cycles",
          "Implemented safeguards against system time changes",
          "Enhanced synchronization between the timer and the UI",
          "Added validation to prevent storing invalid study session durations"
        ]
      }
    ]
  },
  {
    date: "Current Date",
    changes: [
      {
        title: "SEO Optimization for All Landing Pages",
        description: "Enhanced search engine visibility for all landing pages:",
        items: [
          "Added comprehensive meta tags for better search engine indexing",
          "Implemented page-specific keywords for targeted search results",
          "Added Open Graph tags for improved social media sharing",
          "Created canonical URLs to prevent duplicate content issues",
          "Optimized page titles with relevant keywords for each feature",
          "Added structured navigation to improve site crawlability"
        ]
      }
    ]
  },
  {
    date: "March 13, 2024",
    changes: [
      {
        title: "Improved Landing Page Navigation",
        description: "Enhanced the navigation between all landing pages:",
        items: [
          "Created a consistent navigation component shared across all landing pages",
          "Improved feature cards on the main landing page with better descriptions",
          "Added visual feedback on hover for landing page links",
          "Ensured seamless navigation between all feature pages",
          "Made the landing pages more accessible on mobile devices"
        ]
      },
      {
        title: "Added Feature Landing Pages",
        description: "Introduced dedicated landing pages for each major feature:",
        items: [
          "AI Assistant Landing Page - Showcasing AI chat interface and capabilities",
          "Study Groups Landing Page - Highlighting collaborative learning features",
          "Productivity Tools Landing Page - Demonstrating time management tools",
          "Tasks Landing Page - Presenting task organization features",
          "Updated main landing page with feature navigation section"
        ]
      }
    ]
  },
  // ... existing changelog entries ...
];

const ChangelogPage = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <Helmet>
        <title>Changelog - IsotopeAI</title>
        <meta
          name="description"
          content="Track all updates and improvements to IsotopeAI, your AI-powered Physics, Chemistry, and Mathematics doubt solver."
        />
        <meta
          name="keywords"
          content="IsotopeAI changelog, PCM doubt solver updates, educational AI updates, physics tutor updates, chemistry help updates, mathematics solver updates"
        />
      </Helmet>
      <Changelog />
      
      {/* Footer */}
      <footer className="relative z-10 mt-16 border-t border-white/[0.08] bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-3">
                <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-10 h-10 rounded-full" />
                <span className="font-semibold text-white/90">
                  IsotopeAI
                </span>
              </div>
              <p className="text-white/40 max-w-md">
                Your all-in-one platform for AI-powered learning, productivity tools, and collaborative study.
              </p>
            </div>
            
            <div className="flex flex-col md:items-end space-y-4">
              <div className="flex items-center space-x-4">
                <FeedbackWidget className="text-white/60 hover:text-white transition-colors" />
                <span className="text-white/20">|</span>
                <Link to="/changelog" className="text-white/60 hover:text-white transition-colors">
                  Changelog
                </Link>
                <span className="text-white/20">|</span>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-white/60 hover:text-white transition-colors"
                >
                  Contact
                </a>
                <span className="text-white/20">|</span>
                <a 
                  href="https://www.instagram.com/isotope.ai/" 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/60 hover:text-white transition-colors flex items-center gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-instagram"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                  </svg>
                </a>
                <span className="text-white/20">|</span>
                <a 
                  href="https://www.reddit.com/r/Isotope/" 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/60 hover:text-white transition-colors flex items-center gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="9" r="1" />
                    <circle cx="12" cy="15" r="1" />
                    <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                    <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                    <path d="M7.5 13h9" />
                    <path d="M10 16v-3" />
                    <path d="M14 16v-3" />
                  </svg>
                </a>
              </div>
              <p className="text-white/40 text-sm">
                Built with <span className="text-red-500">❤️</span> by a fellow JEEtard
              </p>
            </div>
          </div>
        </div>

        <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent pointer-events-none" />
      </footer>
    </>
  );
};

export default ChangelogPage;