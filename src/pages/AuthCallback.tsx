import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, loading } = useSupabaseAuth();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        setIsProcessing(true);
        
        // Check if this is an error callback
        const errorParam = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        
        if (errorParam) {
          console.error('Auth callback error:', errorParam, errorDescription);
          setError(errorDescription || 'Authentication failed');
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        // Wait for auth context to process the callback
        if (loading) {
          return;
        }

        if (!user) {
          // No user found, redirect to login
          navigate('/login', { replace: true });
          return;
        }

        // Let the auth context handle the rest
        // It will redirect to profile setup or AI page based on profile status
        
      } catch (error) {
        console.error('Error in auth callback:', error);
        setError('Authentication processing failed');
        setTimeout(() => navigate('/login'), 3000);
      } finally {
        setIsProcessing(false);
      }
    };

    handleAuthCallback();
  }, [user, loading, navigate, searchParams]);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen py-2">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Authentication Error</h1>
          <p className="text-gray-700 mb-4">{error}</p>
          <p className="text-sm text-gray-500">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  if (isProcessing || loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen py-2">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold mb-4">Processing Authentication</h1>
          <p className="text-gray-700">Please wait while we complete your sign-in...</p>
        </div>
      </div>
    );
  }

  return null;
};

export default AuthCallback;
