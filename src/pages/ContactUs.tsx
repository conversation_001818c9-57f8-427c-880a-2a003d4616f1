import { Link } from 'react-router-dom';
import { ArrowLeft, Mail, MessageSquare, Github, Twitter, MapPin, Phone } from 'lucide-react';
import { Helmet } from 'react-helmet';
import { <PERSON><PERSON>, Footer } from '@/components/shared';
import { motion } from 'framer-motion';
import { useEffect } from 'react';

const ContactUs = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-[#030303] text-white/90 font-onest">
      <Helmet>
        <title>Contact Us - IsotopeAI</title>
        <meta
          name="description"
          content="Get in touch with the IsotopeAI team. We'd love to hear from you!"
        />
      </Helmet>

      {/* Header */}
      <Header />

      <div className="container mx-auto px-4 py-32 max-w-4xl">
        <div className="mb-8">
          <Link to="/" className="inline-flex items-center text-violet-400 hover:text-violet-300 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </div>

        <div className="space-y-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Contact Us</h1>
            <p className="text-white/60 text-lg">We'd love to hear from you. Get in touch with the IsotopeAI team.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              className="bg-white/5 p-8 rounded-xl border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl font-semibold mb-6">Get in Touch</h2>
              <p className="text-white/70 mb-8">
                Have questions, feedback, or just want to say hello? We'd love to hear from you!
                Here are the ways you can reach out to us.
              </p>

              <div className="space-y-8">
                <div className="flex items-start">
                  <div className="p-3 bg-violet-500/20 rounded-lg mr-4">
                    <Mail className="h-6 w-6 text-violet-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Email Us</h3>
                    <p className="text-white/60 mb-2">For general inquiries and support:</p>
                    <a href="mailto:<EMAIL>" className="text-violet-400 hover:text-violet-300 transition-colors text-lg">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="p-3 bg-violet-500/20 rounded-lg mr-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-violet-400"
                    >
                      <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                      <path d="M17.5 6.5h.01" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Social Media</h3>
                    <p className="text-white/60 mb-2">Follow us for updates and connect with us:</p>
                    <div className="flex flex-col space-y-2">
                      <a
                        href="https://www.instagram.com/isotope.ai/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-violet-400 hover:text-violet-300 transition-colors flex items-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                          <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                          <path d="M17.5 6.5h.01" />
                        </svg>
                        @isotope.ai
                      </a>
                    </div>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="p-3 bg-violet-500/20 rounded-lg mr-4">
                    <MessageSquare className="h-6 w-6 text-violet-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Community</h3>
                    <p className="text-white/60 mb-2">Join our community forum:</p>
                    <a
                      href="https://www.reddit.com/r/Isotope/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-violet-400 hover:text-violet-300 transition-colors"
                    >
                      r/Isotope on Reddit
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white/5 p-8 rounded-xl border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h2 className="text-2xl font-semibold mb-6">Feedback & Support</h2>
              <p className="text-white/70 mb-8">
                We're constantly working to improve IsotopeAI. Your feedback helps us make it better!
              </p>

              <div className="space-y-8">
                <div className="p-6 bg-violet-500/10 rounded-xl">
                  <h3 className="font-semibold text-lg mb-3">Feature Requests & Bug Reports</h3>
                  <p className="text-white/70 mb-4">
                    Have an idea for a new feature or found a bug? Let us know through our feedback portal:
                  </p>
                  <a
                    href="https://isotopeai.featurebase.app"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-violet-600 hover:bg-violet-700 rounded-md transition-colors"
                  >
                    Submit Feedback
                  </a>
                </div>

                <div className="p-6 bg-white/5 rounded-xl border border-white/10">
                  <h3 className="font-semibold text-lg mb-3">Business Inquiries</h3>
                  <p className="text-white/70 mb-2">
                    For partnerships, press, or business-related inquiries:
                  </p>
                  <a href="mailto:<EMAIL>" className="text-violet-400 hover:text-violet-300 transition-colors">
                    <EMAIL>
                  </a>
                </div>

                <div className="p-6 bg-white/5 rounded-xl border border-white/10">
                  <h3 className="font-semibold text-lg mb-3">Response Time</h3>
                  <p className="text-white/70">
                    We typically respond to inquiries within 24-48 hours during business days.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default ContactUs;
