import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, Search, BookOpen, Users, Brain, Target, MessageSquare, FileText, Upload, Sparkles, User, Bot, Github, Instagram, Mail } from "lucide-react";
import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header } from "@/components/shared";
import { Footer } from "@/components/shared";
import { Helmet } from "react-helmet";

const QALanding = () => {
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleGetStarted = () => {
    if (user) {
      navigate('/qa');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted relative overflow-hidden flex flex-col">
      <Helmet>
        <title>Q&A Database for Students | Ask Questions & Get Answers | IsotopeAI</title>
        <meta
          name="description"
          content="Access a comprehensive Q&A database for exam preparation. Ask questions, get answers from experts, and learn from the community. Perfect for JEE, NEET & BITSAT preparation."
        />
        <meta
          name="keywords"
          content="Q&A database, student questions, exam preparation, JEE questions, NEET questions, BITSAT questions, study community, expert answers, student forum, academic Q&A"
        />
        <meta property="og:title" content="Q&A Database for Students | Ask Questions & Get Answers | IsotopeAI" />
        <meta property="og:description" content="Access a comprehensive Q&A database for exam preparation. Ask questions, get answers from experts, and learn from the community." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/qa-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/qa-landing" />
      </Helmet>
      
      {/* Header */}
      <Header />
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 pt-28 md:pt-40 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <motion.div 
                className="inline-block mb-4"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ 
                  duration: 0.5,
                  type: "spring",
                  stiffness: 200
                }}
              >
                <div className="bg-primary/20 p-3 rounded-full inline-block">
                  <img src="/favicon.ico" alt="IsotopeAI Logo" className="w-8 h-8" />
                </div>
              </motion.div>
              
              {/* 100% Free Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="mb-4"
              >
                <span className="bg-green-500/20 text-green-500 font-semibold px-4 py-2 rounded-full inline-flex items-center gap-1 border border-green-500/30">
                  <img src="/favicon.ico" alt="IsotopeAI Logo" className="w-4 h-4" />
                  100% Free Forever
                </span>
              </motion.div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
                Get Detailed Answers to Your Questions
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto lg:mx-0">
                Ask questions and get detailed, step-by-step answers from our advanced AI
              </p>
              
              {user ? (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Go to Q&A Database <ArrowRight className="ml-2" />
                </Button>
              ) : (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Try now (It's 100% FREE) <ArrowRight className="ml-2" />
                </Button>
              )}
            </motion.div>
            
            {/* Right side - Q&A Demo */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="lg:mt-0 mt-8"
            >
              <AnimatedChatDemo />
            </motion.div>
          </div>
        </section>

        {/* Comparison Section */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Why Choose <span className="text-primary">IsotopeAI</span> Over Others?
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Byjus Comparison */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-card rounded-xl p-6 border border-white/10 shadow-lg hover:border-primary/20 transition-colors"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-red-500/10 rounded-lg flex items-center justify-center">
                  <img src="/byjus.png" alt="Byjus" className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold">Byjus</h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Doobi hui company</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Unreliable server</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">No instant answers</p>
                </div>
              </div>
            </motion.div>

            {/* Toppr Comparison */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-card rounded-xl p-6 border border-white/10 shadow-lg hover:border-primary/20 transition-colors"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                  <img src="/toppr.png" alt="Toppr" className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold">Toppr</h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Byjus isko bhi le dooba</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Iske bhi server ke L lag rahe hai</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">No follow-up questions</p>
                </div>
              </div>
            </motion.div>

            {/* Brainly Comparison */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-card rounded-xl p-6 border border-white/10 shadow-lg hover:border-primary/20 transition-colors"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <img src="/brainly.png" alt="Brainly" className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold">Brainly</h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Unreliable answers</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Too many ads</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1">
                    <span className="text-red-500 text-xs">✕</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Zero focus on exams</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Our Advantages */}
          <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-primary/5 rounded-xl p-6 border border-primary/20 shadow-lg"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Brain className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-primary">Instant, Detailed AI Answers</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Get detailed, step-by-step solutions instantly from our advanced AI system. No waiting for community responses.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-primary/5 rounded-xl p-6 border border-primary/20 shadow-lg"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-primary">Follow-up Questions</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Ask follow-up questions to get deeper understanding. Our AI remembers context and provides coherent explanations.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-primary/5 rounded-xl p-6 border border-primary/20 shadow-lg"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-primary">100% Free Forever</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Built by a student for students. No ads, no hidden costs, no premium features. Everything is completely free.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Powerful Features for <span className="text-primary">Effective Learning</span>
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<MessageSquare className="w-8 h-8" />}
              title="Smart Q&A Interface"
              description="Ask questions naturally and get detailed, step-by-step answers from our AI"
              delay={0.1}
            />
            <FeatureCard
              icon={<BookOpen className="w-8 h-8" />}
              title="Comprehensive Database"
              description="Access a vast collection of questions and answers covering various subjects"
              delay={0.2}
            />
            <FeatureCard
              icon={<Brain className="w-8 h-8" />}
              title="AI-Powered Answers"
              description="Get detailed, step-by-step solutions from our advanced AI system"
              delay={0.3}
            />
            <FeatureCard
              icon={<Search className="w-8 h-8" />}
              title="Smart Search"
              description="Find relevant questions and answers quickly with our intelligent search"
              delay={0.4}
            />
            <FeatureCard
              icon={<Target className="w-8 h-8" />}
              title="Exam-Focused"
              description="Questions and answers specifically tailored for competitive exams"
              delay={0.5}
            />
            <FeatureCard
              icon={<Sparkles className="w-8 h-8" />}
              title="Step-by-Step Solutions"
              description="Get detailed explanations with every step clearly explained"
              delay={0.6}
            />
          </div>
        </section>

        {/* Before/After Comparison Section */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Real <span className="text-primary">Results</span> from Real Students
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="bg-card rounded-xl p-6 border border-white/10 shadow-lg">
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  <span className="bg-red-500/20 text-red-500 p-2 rounded-full mr-3">
                    <ArrowRight className="w-5 h-5" />
                  </span>
                  Before Using Q&A Database
                </h3>
                
                <div className="space-y-6">
                  <div className="bg-background/50 p-4 rounded-lg">
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Question Resolution Time</span>
                      <span className="font-medium">2-3 days</span>
                    </div>
                    <div className="h-2 bg-white/5 rounded-full">
                      <div className="h-full w-[30%] bg-red-400 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg">
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Answer Quality</span>
                      <span className="font-medium">65%</span>
                    </div>
                    <div className="h-2 bg-white/5 rounded-full">
                      <div className="h-full w-[65%] bg-red-400 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg">
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Solution Clarity</span>
                      <span className="font-medium">40%</span>
                    </div>
                    <div className="h-2 bg-white/5 rounded-full">
                      <div className="h-full w-[40%] bg-red-400 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="bg-card rounded-xl p-6 border border-primary/20 shadow-lg">
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  <span className="bg-green-500/20 text-green-500 p-2 rounded-full mr-3">
                    <ArrowRight className="w-5 h-5" />
                  </span>
                  After Using Q&A Database
                </h3>
                
                <div className="space-y-6">
                  <motion.div 
                    className="bg-background/50 p-4 rounded-lg"
                    initial={{ width: "30%" }}
                    whileInView={{ width: "100%" }}
                    viewport={{ once: true }}
                    transition={{ duration: 1, delay: 0.5 }}
                  >
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Question Resolution Time</span>
                      <span className="font-medium">Instant</span>
                    </div>
                    <div className="h-2 bg-white/5 rounded-full">
                      <motion.div 
                        className="h-full bg-green-400 rounded-full"
                        initial={{ width: "30%" }}
                        whileInView={{ width: "100%" }}
                        viewport={{ once: true }}
                        transition={{ duration: 1, delay: 0.5 }}
                      ></motion.div>
                    </div>
                  </motion.div>
                  
                  <motion.div 
                    className="bg-background/50 p-4 rounded-lg"
                    initial={{ width: "65%" }}
                    whileInView={{ width: "100%" }}
                    viewport={{ once: true }}
                    transition={{ duration: 1, delay: 0.7 }}
                  >
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Answer Quality</span>
                      <span className="font-medium">95%</span>
                    </div>
                    <div className="h-2 bg-white/5 rounded-full">
                      <motion.div 
                        className="h-full bg-green-400 rounded-full"
                        initial={{ width: "65%" }}
                        whileInView={{ width: "95%" }}
                        viewport={{ once: true }}
                        transition={{ duration: 1, delay: 0.7 }}
                      ></motion.div>
                    </div>
                  </motion.div>
                  
                  <motion.div 
                    className="bg-background/50 p-4 rounded-lg"
                    initial={{ width: "40%" }}
                    whileInView={{ width: "100%" }}
                    viewport={{ once: true }}
                    transition={{ duration: 1, delay: 0.9 }}
                  >
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Solution Clarity</span>
                      <span className="font-medium">95%</span>
                    </div>
                    <div className="h-2 bg-white/5 rounded-full">
                      <motion.div 
                        className="h-full bg-green-400 rounded-full"
                        initial={{ width: "40%" }}
                        whileInView={{ width: "95%" }}
                        viewport={{ once: true }}
                        transition={{ duration: 1, delay: 0.9 }}
                      ></motion.div>
                    </div>
                  </motion.div>
                </div>
                
                <div className="mt-6 text-primary text-sm italic">
                  "The AI-powered Q&A database has transformed how I learn. I get instant, detailed step-by-step solutions!"
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Question-Answer Process Animation */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            How It <span className="text-primary">Works</span>
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-card rounded-xl p-6 border border-white/10 shadow-lg relative z-10"
            >
              <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg shadow-lg shadow-primary/20 hidden md:flex">1</div>
              
              <div className="mb-6 flex justify-center">
                <motion.div
                  animate={{ 
                    y: [0, -5, 0],
                    opacity: [1, 0.8, 1]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "loop"
                  }}
                  className="bg-primary/10 p-4 rounded-full"
                >
                  <Upload className="w-10 h-10 text-primary" />
                </motion.div>
              </div>
              
              <h3 className="text-xl font-semibold mb-3 text-center">Ask Your Question</h3>
              <p className="text-muted-foreground text-center">
                Type your question or upload an image of your problem
              </p>
              
              <div className="mt-6 bg-background/50 p-4 rounded-lg">
                <motion.div
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  viewport={{ once: true }}
                  transition={{ duration: 1.5 }}
                  className="h-1 bg-primary/40 rounded-full"
                ></motion.div>
              </div>
            </motion.div>
            
            {/* Step 2 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-card rounded-xl p-6 border border-white/10 shadow-lg relative z-10"
            >
              <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg shadow-lg shadow-primary/20 hidden md:flex">2</div>
              
              <div className="mb-6 flex justify-center">
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, 0]
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "loop"
                  }}
                  className="bg-primary/10 p-4 rounded-full"
                >
                  <Brain className="w-10 h-10 text-primary" />
                </motion.div>
              </div>
              
              <h3 className="text-xl font-semibold mb-3 text-center">Get AI-Powered Answers</h3>
              <p className="text-muted-foreground text-center">
                Receive detailed, step-by-step solutions from our advanced AI
              </p>
              
              <div className="mt-6 bg-background/50 p-4 rounded-lg">
                <motion.div
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  viewport={{ once: true }}
                  transition={{ duration: 1.5, delay: 0.5 }}
                  className="h-1 bg-primary/40 rounded-full"
                ></motion.div>
              </div>
            </motion.div>
            
            {/* Step 3 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-card rounded-xl p-6 border border-white/10 shadow-lg relative z-10"
            >
              <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg shadow-lg shadow-primary/20 hidden md:flex">3</div>
              
              <div className="mb-6 flex justify-center">
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, 0]
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "loop"
                  }}
                  className="bg-primary/10 p-4 rounded-full"
                >
                  <FileText className="w-10 h-10 text-primary" />
                </motion.div>
              </div>
              
              <h3 className="text-xl font-semibold mb-3 text-center">Learn & Practice</h3>
              <p className="text-muted-foreground text-center">
                Learn from detailed solutions and practice with similar problems
              </p>
              
              <div className="mt-6 bg-background/50 p-4 rounded-lg">
                <motion.div
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  viewport={{ once: true }}
                  transition={{ duration: 1.5, delay: 1 }}
                  className="h-1 bg-primary/40 rounded-full"
                ></motion.div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4 py-20 text-center relative z-10">
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-3xl p-12 backdrop-blur-sm border border-primary/20">
            <motion.h2 
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Ready to Get Started?
            </motion.h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of students already using our Q&A database
            </p>
            
            {user ? (
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 rounded-full px-8 py-6 h-auto text-lg shadow-lg shadow-primary/20"
                onClick={handleGetStarted}
              >
                Go to Q&A Database <ArrowRight className="ml-2" />
              </Button>
            ) : (
              <div className="flex justify-center">
                <SignIn />
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

// Feature Card Component
const FeatureCard = ({ icon, title, description, delay }: { icon: React.ReactNode; title: string; description: string; delay: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay }}
      className="bg-card rounded-xl p-6 border border-white/10 shadow-lg hover:border-primary/20 transition-colors"
    >
      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </motion.div>
  );
};

// Animated Chat Demo Component
const AnimatedChatDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const steps = [
    {
      user: "How do I solve this physics problem about projectile motion?",
      ai: "Let me help you understand projectile motion step by step..."
    },
    {
      user: "Can you explain the concept of momentum?",
      ai: "I'll break down momentum and its applications in physics..."
    },
    {
      user: "What's the difference between speed and velocity?",
      ai: "Let me explain the key differences between speed and velocity..."
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % steps.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-card rounded-xl p-6 border border-white/10 shadow-lg">
      <div className="space-y-4">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <User className="w-4 h-4 text-primary" />
              </div>
              <div className="flex-1">
                <div className="bg-background/50 p-4 rounded-lg">
                  <p className="text-sm">{steps[currentStep].user}</p>
                </div>
              </div>
            </div>
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <Bot className="w-4 h-4 text-primary" />
              </div>
              <div className="flex-1">
                <div className="bg-background/50 p-4 rounded-lg">
                  <p className="text-sm">{steps[currentStep].ai}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default QALanding; 