import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link, useNavigate, useLocation, useParams } from "react-router-dom";
import { motion } from "framer-motion";
import { ArrowRight, Users, UserPlus, MessageCircle, Calendar, BookOpen, Share2 } from "lucide-react";
import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header } from "@/components/shared";
import { Helmet } from "react-helmet";
import { useToast } from "@/components/ui/use-toast";
import { Footer } from "@/components/shared";

const InviteLanding = () => {
  const [inviteCode, setInviteCode] = useState<string | null>(null);
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const { toast } = useToast();

  // Extract invite code from URL
  useEffect(() => {
    // First check if code is in the path parameter
    const pathCode = params.code;
    if (pathCode) {
      setInviteCode(pathCode);
      // Store the invite code in localStorage for after authentication
      localStorage.setItem('pendingInviteCode', pathCode);
      return;
    }
    
    // If not in path, check query parameters
    const searchParams = new URLSearchParams(location.search);
    const queryCode = searchParams.get('code');
    if (queryCode) {
      setInviteCode(queryCode);
      // Store the invite code in localStorage for after authentication
      localStorage.setItem('pendingInviteCode', queryCode);
    }
  }, [location.search, params.code]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleJoinGroup = async () => {
    if (user) {
      // If user is already logged in, redirect to join handler
      navigate(`/join?code=${inviteCode}`);
    } else {
      // If user is not logged in, initiate Google sign-in
      // The AuthContext will handle the redirect to join handler after login
      try {
        await signInWithGoogle();
        // Note: We don't need to navigate here as AuthContext will handle the redirect
        // based on the pendingInviteCode in localStorage
      } catch (error) {
        console.error('Error signing in:', error);
        toast({
          title: "Sign-in Error",
          description: "Failed to sign in. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#0f172a] to-[#1e293b] relative overflow-hidden flex flex-col">
      <Helmet>
        <title>You've Been Invited to Join a Study Group | IsotopeAI</title>
        <meta
          name="description"
          content="Join a study group on IsotopeAI to collaborate, share resources, and accelerate your learning. Sign up now to accept the invitation."
        />
      </Helmet>
      
      {/* Header */}
      <Header />
      
      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-600/20 rounded-full filter blur-[100px] animate-blob"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-pink-600/20 rounded-full filter blur-[100px] animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/2 w-96 h-96 bg-cyan-600/20 rounded-full filter blur-[100px] animate-blob animation-delay-4000"></div>
      </div>
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 pt-28 md:pt-40 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <motion.div 
                className="inline-block mb-4"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ 
                  duration: 0.5,
                  type: "spring",
                  stiffness: 200
                }}
              >
                <div className="bg-indigo-500/20 p-3 rounded-full inline-block">
                  <UserPlus className="w-8 h-8 text-indigo-400" />
                </div>
              </motion.div>
              
              {/* Invite Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="mb-4"
              >
                <span className="bg-indigo-500/20 text-indigo-400 font-semibold px-4 py-2 rounded-full inline-flex items-center gap-1 border border-indigo-500/30">
                  <Users className="w-4 h-4" />
                  You've Been Invited!
                </span>
              </motion.div>
              
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Join a Study Group on <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-pink-400">IsotopeAI</span>
              </h1>
              <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto lg:mx-0">
                Someone has invited you to join their study group. Sign up now to collaborate, share resources, and accelerate your learning together.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 rounded-full px-6 py-6 h-auto text-lg shadow-lg shadow-indigo-500/20"
                  onClick={handleJoinGroup}
                >
                  {user ? "Accept Invitation" : "Sign Up & Join Group"} <ArrowRight className="ml-2" />
                </Button>
                
                <Link to="/">
                  <Button 
                    size="lg" 
                    variant="outline"
                    className="rounded-full px-6 py-6 h-auto text-lg border-white/10 bg-white/5 hover:bg-white/10"
                  >
                    Learn More About IsotopeAI
                  </Button>
                </Link>
              </div>
            </motion.div>
            
            {/* Right side - Group Preview */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="lg:mt-0 mt-8"
            >
              <div className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 rounded-xl p-6 border border-white/10 shadow-xl backdrop-blur-sm">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-full">
                      <Users className="w-5 h-5 text-indigo-400" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">Study Group</h3>
                      <p className="text-sm text-white/60">Collaborative Learning Platform</p>
                    </div>
                  </div>
                  {inviteCode && (
                    <span className="bg-indigo-500/20 text-indigo-400 px-2 py-1 rounded-full text-xs border border-indigo-500/30">
                      Invite: {inviteCode}
                    </span>
                  )}
                </div>
                
                <div className="space-y-4">
                  <div className="bg-white/5 p-4 rounded-lg border border-white/10">
                    <h4 className="font-medium mb-2 flex items-center gap-2 text-white">
                      <BookOpen className="w-4 h-4 text-indigo-400" /> What You'll Get
                    </h4>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-indigo-400 rounded-full"></div>
                        Collaborative study sessions
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-indigo-400 rounded-full"></div>
                        Shared resources and materials
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-indigo-400 rounded-full"></div>
                        Group discussions and problem-solving
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-white/5 p-4 rounded-lg border border-white/10">
                    <h4 className="font-medium mb-2 flex items-center gap-2 text-white">
                      <Calendar className="w-4 h-4 text-indigo-400" /> Group Activities
                    </h4>
                    <p className="text-white/70">Schedule study sessions, track progress, and set goals together</p>
                  </div>
                  
                  <div className="bg-white/5 p-4 rounded-lg border border-white/10">
                    <h4 className="font-medium mb-2 flex items-center gap-2 text-white">
                      <MessageCircle className="w-4 h-4 text-indigo-400" /> Real-time Chat
                    </h4>
                    <div className="text-sm text-white/70">
                      <p>Ask questions, share insights, and get help from group members</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 flex justify-center">
                  <Button 
                    className="w-full bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600"
                    onClick={handleJoinGroup}
                  >
                    {user ? "Join Now" : "Sign Up & Join"}
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center text-white"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Why Join <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-pink-400">IsotopeAI</span> Study Groups?
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Users className="w-8 h-8" />}
              title="Collaborative Learning"
              description="Form groups with peers and learn together effectively"
              delay={0.1}
            />
            <FeatureCard
              icon={<Share2 className="w-8 h-8" />}
              title="Resource Sharing"
              description="Share study materials, notes, and helpful resources"
              delay={0.2}
            />
            <FeatureCard
              icon={<MessageCircle className="w-8 h-8" />}
              title="Group Discussions"
              description="Engage in topic-focused discussions and problem-solving"
              delay={0.3}
            />
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <div className="bg-gradient-to-br from-indigo-900/30 to-slate-900/30 rounded-2xl p-8 md:p-12 border border-white/10 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Ready to Join?</h2>
            <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
              Accept the invitation now and start collaborating with your study group
            </p>
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 rounded-full px-8 py-6 h-auto text-lg shadow-lg shadow-indigo-500/20"
              onClick={handleJoinGroup}
            >
              {user ? "Join Study Group" : "Sign Up & Join Group"} <ArrowRight className="ml-2" />
            </Button>
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

const FeatureCard = ({ 
  icon, 
  title, 
  description, 
  delay = 0 
}: { 
  icon: React.ReactNode; 
  title: string; 
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
      className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 p-6 rounded-xl border border-white/10 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] group"
    >
      <div className="bg-indigo-500/20 p-3 rounded-full inline-block mb-4 group-hover:bg-indigo-500/30 transition-colors duration-300">
        <div className="text-indigo-400 group-hover:text-indigo-300 transition-colors duration-300">
          {icon}
        </div>
      </div>
      <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-white/90 transition-colors duration-300">{title}</h3>
      <p className="text-white/70 group-hover:text-white/80 transition-colors duration-300">{description}</p>
    </motion.div>
  );
};

export default InviteLanding; 