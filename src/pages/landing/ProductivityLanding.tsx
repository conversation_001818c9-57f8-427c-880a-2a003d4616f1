import { But<PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, Timer, BarChart, Calendar, Target, Zap, LineChart, Menu, X, Instagram, Mail } from "lucide-react";
import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header } from "@/components/shared";
import { Footer } from "@/components/shared";
import { Helmet } from "react-helmet";

// Animation variants
const fadeUpVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 1,
      delay: 0.5 + i * 0.2,
      ease: [0.25, 0.4, 0.25, 1],
    },
  }),
};

const ProductivityLanding = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/productivity');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030303] font-onest flex flex-col min-h-screen">
      <Helmet>
        <title>Study Productivity Tools & Time Management | IsotopeAI</title>
        <meta
          name="description"
          content="Boost your study productivity with our time management tools. Track progress, use Pomodoro timer, set goals, and visualize your study patterns."
        />
        <meta
          name="keywords"
          content="study productivity, pomodoro timer, study time management, focus timer, progress tracking, study analytics, study scheduler, goal tracking, focus mode, performance insights, JEE preparation tools, NEET study tools"
        />
        <meta property="og:title" content="Study Productivity Tools & Time Management | IsotopeAI" />
        <meta property="og:description" content="Boost your study productivity with our time management tools. Track progress, use Pomodoro timer, set goals, and visualize your study patterns." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/productivity-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/productivity-landing" />
      </Helmet>
      
      {/* Header */}
      <Header />
      
      {/* Background Elements */}
      <GlowingBackground />
      <FloatingElements />
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <div className="text-center max-w-6xl mx-auto">
              {/* Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
              >
                <Zap className="h-4 w-4 text-emerald-400" />
                <span className="font-medium">100% Free Forever</span>
              </motion.div>

              {/* Main Heading */}
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 md:mb-8 tracking-tight leading-tight"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Boost Your{" "}
                </span>
                <motion.span
                  className="relative bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-rose-400"
                  initial={{ backgroundPosition: "0% 50%" }}
                  animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                  transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                >
                  Study Productivity
                </motion.span>
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-lg sm:text-xl md:text-2xl text-white/60 mb-8 md:mb-12 max-w-3xl mx-auto leading-relaxed"
              >
                Track your progress, manage your time, and achieve your study goals effectively
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8"
              >
                {user ? (
                  <Button
                    asChild
                    className="bg-emerald-500/80 hover:bg-emerald-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                  >
                    <div onClick={handleGetStarted} className="cursor-pointer">
                      <span className="relative z-10 flex items-center">
                        Go to Productivity Tools
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                      <span className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-violet-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </Button>
                ) : (
                  <Button
                    asChild
                    className="bg-emerald-500/80 hover:bg-emerald-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                  >
                    <div onClick={handleGetStarted} className="cursor-pointer">
                      <span className="relative z-10 flex items-center">
                        Try now (It's 100% FREE)
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                      <span className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-violet-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </Button>
                )}
              </motion.div>
            </div>
          </div>
        </section>
        {/* Features Grid */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              custom={0}
              variants={fadeUpVariants}
              className="text-center mb-16"
            >
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Powerful Features for{" "}
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-rose-400">
                  Effective Productivity
                </span>
              </h2>
              <p className="text-lg sm:text-xl text-white/60 max-w-3xl mx-auto leading-relaxed">
                Everything you need to maximize your study efficiency and achieve your academic goals
              </p>
            </motion.div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              <FeatureCard
                icon={<Timer className="w-6 h-6" />}
                title="Pomodoro Timer"
                description="Stay focused with customizable study and break intervals"
                delay={0.1}
                gradient="from-emerald-500/30 to-emerald-600/30"
              />
              <FeatureCard
                icon={<BarChart className="w-6 h-6" />}
                title="Progress Analytics"
                description="Visualize your study patterns and improvements"
                delay={0.2}
                gradient="from-violet-500/30 to-violet-600/30"
              />
              <FeatureCard
                icon={<Calendar className="w-6 h-6" />}
                title="Study Scheduler"
                description="Plan your study sessions and set reminders"
                delay={0.3}
                gradient="from-rose-500/30 to-rose-600/30"
              />
              <FeatureCard
                icon={<Target className="w-6 h-6" />}
                title="Goal Tracking"
                description="Set and monitor your study objectives"
                delay={0.4}
                gradient="from-blue-500/30 to-blue-600/30"
              />
              <FeatureCard
                icon={<Zap className="w-6 h-6" />}
                title="Focus Mode"
                description="Eliminate distractions during study sessions"
                delay={0.5}
                gradient="from-emerald-500/30 to-violet-500/30"
              />
              <FeatureCard
                icon={<LineChart className="w-6 h-6" />}
                title="Performance Insights"
                description="Get detailed analytics about your study habits"
                delay={0.6}
                gradient="from-violet-500/30 to-rose-500/30"
              />
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              custom={0}
              variants={fadeUpVariants}
              className="text-center mb-16"
            >
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Proven{" "}
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-rose-400">
                  Results
                </span>
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
              <StatCard
                number="25%"
                label="Average Productivity Increase"
                delay={0.1}
              />
              <StatCard
                number="2x"
                label="Better Focus Duration"
                delay={0.2}
              />
              <StatCard
                number="45+"
                label="Minutes Saved Per Study Session"
                delay={0.3}
              />
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-violet-950/20 via-transparent to-emerald-950/20"></div>
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <div className="container mx-auto px-4 md:px-6 text-center relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
              >
                <Target className="h-4 w-4 text-emerald-400" />
                <span className="font-medium">Ready to Excel?</span>
              </motion.div>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 md:mb-8 tracking-tight leading-tight"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Ready to Maximize Your{" "}
                </span>
                <motion.span
                  className="relative bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-rose-400"
                  initial={{ backgroundPosition: "0% 50%" }}
                  animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                  transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                >
                  Study Time?
                </motion.span>
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-lg sm:text-xl md:text-2xl text-white/60 mb-8 md:mb-12 max-w-3xl mx-auto leading-relaxed"
              >
                Join thousands of students who have improved their study efficiency
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8"
              >
                {user ? (
                  <Button
                    asChild
                    className="bg-emerald-500/80 hover:bg-emerald-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                  >
                    <div onClick={handleGetStarted} className="cursor-pointer">
                      <span className="relative z-10 flex items-center">
                        Go to Dashboard
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                      <span className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-violet-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </Button>
                ) : (
                  <div className="flex justify-center">
                    <SignIn />
                  </div>
                )}
              </motion.div>

              {/* Trust indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-wrap justify-center gap-6 text-sm text-white/60"
              >
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-violet-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                  <span>Instant access</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-rose-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                  <span>100% free forever</span>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay = 0,
  gradient
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
  gradient: string;
}) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      custom={delay}
      variants={fadeUpVariants}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      className="relative group"
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-lg`}></div>
      <div className="bg-white/[0.03] backdrop-blur-md p-6 md:p-8 rounded-2xl shadow-lg border border-white/[0.08] relative h-full flex flex-col group-hover:border-white/20 transition-colors duration-300">
        <div className="flex items-start gap-4 mb-5">
          <div className={`bg-gradient-to-br ${gradient} p-4 rounded-xl relative border border-white/10 shrink-0`}>
            <motion.div
              animate={{ rotate: [0, 5, 0, -5, 0] }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-white"
            >
              {icon}
            </motion.div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-white/90 mb-2">{title}</h3>
            <p className="text-white/60">{description}</p>
          </div>
        </div>

        {/* Bottom flourish */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-b-2xl`}></div>
      </div>
    </motion.div>
  );
};

const StatCard = ({ number, label, delay }: { number: string; label: string; delay: number }) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      custom={delay}
      variants={fadeUpVariants}
      whileHover={{ y: -5 }}
      className="bg-white/[0.03] backdrop-blur-md p-8 rounded-2xl border border-white/[0.08] text-center shadow-xl relative overflow-hidden group"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      <div className="relative">
        <h3 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-rose-400 mb-2">{number}</h3>
        <p className="text-base text-white/70">{label}</p>
      </div>
    </motion.div>
  );
};

// Background Components
const GlowingBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Large gradient orbs */}
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-br from-emerald-500/20 via-violet-500/10 to-transparent rounded-full blur-3xl opacity-70" />
      <div className="absolute top-1/4 -right-40 w-96 h-96 bg-gradient-to-bl from-violet-500/20 via-rose-500/10 to-transparent rounded-full blur-3xl opacity-70" />
      <div className="absolute -bottom-40 left-1/4 w-80 h-80 bg-gradient-to-tr from-rose-500/20 via-emerald-500/10 to-transparent rounded-full blur-3xl opacity-70" />

      {/* Radial gradients */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-full h-full bg-gradient-radial from-violet-500/5 via-transparent to-transparent" />
      <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-radial from-emerald-500/5 via-transparent to-transparent" />
    </div>
  );
};

const ElegantShape = ({ className, delay = 0 }: { className: string; delay?: number }) => {
  return (
    <motion.div
      className={`absolute rounded-full border border-white/10 ${className}`}
      animate={{
        rotate: [0, 360],
        scale: [1, 1.1, 1],
        opacity: [0.3, 0.6, 0.3],
      }}
      transition={{
        duration: 20,
        repeat: Infinity,
        ease: "linear",
        delay,
      }}
    />
  );
};

const FloatingElements = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <ElegantShape className="top-20 left-20 w-32 h-32" delay={0} />
      <ElegantShape className="top-40 right-32 w-24 h-24" delay={5} />
      <ElegantShape className="bottom-32 left-40 w-28 h-28" delay={10} />
      <ElegantShape className="bottom-20 right-20 w-20 h-20" delay={15} />

      {/* Floating dots */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-2 h-2 bg-emerald-400/60 rounded-full"
        animate={{
          y: [0, -20, 0],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-1/3 right-1/3 w-3 h-3 bg-violet-400/60 rounded-full"
        animate={{
          y: [0, 15, 0],
          opacity: [0.3, 0.7, 0.3],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-2 h-2 bg-rose-400/60 rounded-full"
        animate={{
          y: [0, -10, 0],
          opacity: [0.5, 0.9, 0.5],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />
    </div>
  );
};

export default ProductivityLanding;