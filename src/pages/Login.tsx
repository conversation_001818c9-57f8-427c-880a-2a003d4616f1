import { useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'

export default function Login() {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, loading, signInWithGoogle } = useSupabaseAuth()

  useEffect(() => {
    if (!loading && user) {
      // Check for returnUrl in query parameters
      const searchParams = new URLSearchParams(location.search)
      const returnUrl = searchParams.get('returnUrl')
      
      // Navigate to the return URL, intended path from state, or default to AI page
      const intendedPath = returnUrl || location.state?.from || '/ai'
      navigate(intendedPath, { replace: true })
    }
  }, [user, loading, navigate, location])

  const handleLogin = async () => {
    try {
      await signInWithGoogle()
    } catch (error) {
      console.error('Login error:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Welcome Back</h1>
          <p className="mt-2 text-muted-foreground">
            Sign in to continue to IsotopeAI
          </p>
        </div>

        <div className="space-y-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleLogin}
          >
            Continue with Google
          </Button>
        </div>
      </div>
    </div>
  )
} 