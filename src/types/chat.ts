export interface Message {
  content: string;
  isUser: boolean;
  image?: {
    url: string;
    name: string;
    urlKey?: string;
    publicId?: string;
  };
}

export interface Comment {
  id: string;
  content: string;
  author: string; // Original author name (might differ from current username)
  authorId: string;
  authorUsername?: string; // Embedded username for display
  authorPhotoURL?: string; // Embedded photo URL for display
  timestamp: number;
  photoURL?: string; // Kept for potential backward compatibility or other uses
  parentId?: string;
  replies?: Comment[];
}

export interface ChatInterfaceProps {
  initialQuery?: string;
  initialImage?: File;
  isReadOnly?: boolean;
  initialChatId?: string | null;
  currentChatId?: string | null;
  isNewChat?: boolean;
  onChatInitialized?: () => void;
}

export interface ChatHistory {
  id: string;
  timestamp: number;
  messages: Message[];
  preview: string;
  isStarred?: boolean;
  isPinned?: boolean;
  comments?: Comment[];
}
