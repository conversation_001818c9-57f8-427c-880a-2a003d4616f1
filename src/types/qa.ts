export interface Question {
  id: string;
  title: string;
  content: string;
  subject: Subject;
  topic: string;
  subtopic?: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  questionType: 'Numerical' | 'Theory' | 'Conceptual' | 'Multi-step';
  examRelevance: ExamType[];
  tags: string[];
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  views: number;
  upvotes: number;
  downvotes: number;
  image?: {
    url: string;
    name: string;
  };
  relatedQuestions?: string[]; // Array of question IDs
  canonicalUrl?: string; // For SEO
}

export interface Answer {
  id: string;
  questionId: string;
  content: string;
  steps: AnswerStep[];
  alternativeApproaches?: AnswerStep[][];
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  upvotes: number;
  downvotes: number;
  isVerified: boolean;
  verifiedBy?: string;
}

export interface AnswerStep {
  content: string;
  image?: {
    url: string;
    name: string;
  };
}

export interface Comment {
  id: string;
  questionId: string;
  answerId?: string;
  content: string;
  author: string;
  authorId: string;
  timestamp: number;
  photoURL?: string;
  parentId?: string;
  replies?: Comment[];
}

export type Subject = 'Physics' | 'Chemistry' | 'Mathematics' | 'Biology';

export type ExamType = 'JEE Main' | 'JEE Advanced' | 'NEET' | 'BITSAT';

export interface QASearchParams {
  query?: string;
  subject?: Subject;
  topic?: string;
  difficulty?: 'Easy' | 'Medium' | 'Hard';
  questionType?: 'Numerical' | 'Theory' | 'Conceptual' | 'Multi-step';
  examType?: ExamType;
  tags?: string[];
  limit?: number;
  offset?: number;
}

export interface QASearchResult {
  questions: Question[];
  total: number;
  hasMore: boolean;
}

export interface QAMetadata {
  subjects: Subject[];
  topics: Record<Subject, string[]>;
  subtopics: Record<string, string[]>; // Key is topic
  tags: string[];
}

export interface SharedChatSEO {
  id: string;               // Document ID (same as the aiChats document ID)
  title: string;            // Generated title for the conversation
  slug: string;             // URL-friendly identifier generated from the title
  createdAt: number;        // Timestamp for sorting and sitemaps
  updatedAt: number;        // Timestamp for when content was last modified
  userId: string;           // The user who created this chat
  viewCount: number;        // Track popularity
  chatMessages: ChatMessage[]; // Full conversation thread
  tags?: string[];          // Optional categorization
  status: 'approved' | 'pending' | 'rejected'; // For moderation
  image?: {                 // Optional image data (from first message)
    url: string;
    name: string;
    publicId?: string;
  };
  canonicalUrl?: string;    // For SEO
  requiresAuth?: boolean;   // Whether the chat requires authentication to view
}

// ChatMessage interface for the messages within a chat
export interface ChatMessage {
  content: string;
  isUser: boolean;
  timestamp?: number;
  image?: {
    name: string;
    url?: string;
    urlKey?: string;
    publicId?: string;
  };
}

export interface PublicQnA {
  id: string;
  questionText: string;
  answerText: string;
  slug: string;
  createdAt: number;
  updatedAt: number;
  userId: string;
  viewCount: number;
  tags?: string[];
} 