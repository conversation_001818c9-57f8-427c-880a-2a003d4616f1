
declare global {
  interface Window {
    Featurebase?: (
      action: string,
      data: {
        organization: string;
        email?: string;
        name?: string;
        userId: string;
        profilePicture?: string;
        userHash?: string;
        customFields?: Record<string, any>;
        companies?: Array<{
          id: string;
          name: string;
          monthlySpend?: number;
          createdAt?: string;
          customFields?: Record<string, any>;
        }>;
        locale?: string;
      },
      callback?: (error?: any) => void
    ) => void;
  }
}

export {}; 
