/* Chart dark mode styles */
.dark .chart-with-theme .MuiChartsAxis-label,
.dark .chart-with-theme .MuiChartsAxis-tickLabel,
.dark .chart-with-theme .MuiChartsLegend-series,
.dark .chart-with-theme text {
  fill: rgba(255, 255, 255, 0.9) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.dark .chart-with-theme .MuiChartsTooltip-table {
  background-color: rgba(30, 30, 60, 0.9) !important;
  color: #ffffff !important;
}

.dark .chart-with-theme .MuiChartsTooltip-valueLabel,
.dark .chart-with-theme .MuiChartsTooltip-markLabel {
  color: #ffffff !important;
}

/* Ensure all text elements in charts are visible in dark mode */
.dark .chart-with-theme g text,
.dark .chart-with-theme svg text,
.dark .chart-with-theme .MuiChartsLegend-root text,
.dark .chart-with-theme .MuiChartsAxis-root text {
  fill: rgba(255, 255, 255, 0.9) !important;
}

/* Force text color for all SVG text elements */
.dark svg text {
  fill: rgba(255, 255, 255, 0.9) !important;
}

/* Light mode styles for consistency */
.light .chart-with-theme .MuiChartsAxis-label,
.light .chart-with-theme .MuiChartsAxis-tickLabel,
.light .chart-with-theme .MuiChartsLegend-series,
.light .chart-with-theme text {
  fill: rgba(0, 0, 0, 0.8) !important;
  color: rgba(0, 0, 0, 0.8) !important;
}
