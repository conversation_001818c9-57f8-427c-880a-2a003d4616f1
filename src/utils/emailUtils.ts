import * as SibApiV3Sdk from '@getbrevo/brevo';

// Initialize the Brevo API client
const apiKey = import.meta.env.VITE_BREVO_API_KEY;

// Configure API key authorization via header forwarding
const brevoApiHeaders = {
  'api-key': apiKey,
  'content-type': 'application/json',
  'accept': 'application/json'
};

// Default list ID for all new users (list ID 6)
const DEFAULT_LIST_ID = 6;
// Template ID for welcome email (replace with your actual template ID)
const WELCOME_TEMPLATE_ID = 5;

/**
 * Test function to trigger welcome email
 * Open browser console and run: window.testWelcomeEmail('<EMAIL>', 'Test User')
 */
export const testWelcomeEmail = async (email: string, displayName: string) => {
  console.log('Testing welcome email for:', email);
  try {
    const result = await sendWelcomeEmail(email, displayName);
    console.log('Test result:', result ? 'Success!' : 'Failed');
  } catch (error) {
    console.error('Test error:', error);
  }
};

/**
 * Test function to just add a contact to list 6
 * Open browser console and run: window.testAddContact('<EMAIL>', 'Test User')
 */
export const testAddContact = async (email: string, displayName: string) => {
  console.log('Testing adding contact to list 6:', email);
  try {
    const result = await createOrUpdateContact(email, displayName);
    console.log('Add contact test result:', result ? 'Success!' : 'Failed');
  } catch (error) {
    console.error('Add contact test error:', error);
  }
};

// Expose the test functions to the window object for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testWelcomeEmail = testWelcomeEmail;
  (window as any).testAddContact = testAddContact;
}

/**
 * Creates or updates a contact in Brevo
 * @param email - User's email address
 * @param displayName - User's display name
 * @param listIds - Optional array of list IDs to add the contact to
 * @returns Promise that resolves when the contact has been created/updated
 */
export const createOrUpdateContact = async (
  email: string, 
  displayName: string, 
  listIds: number[] = []
): Promise<boolean> => {
  try {
    if (!apiKey) {
      console.error('Brevo API key is not configured');
      return false;
    }

    // Always add the default list ID (6) if not already included
    const finalListIds = listIds.includes(DEFAULT_LIST_ID) 
      ? listIds 
      : [...listIds, DEFAULT_LIST_ID];
    
    console.log(`Adding contact to lists: ${finalListIds.join(', ')}`);

    // Create the contact payload
    const payload = {
      email,
      attributes: {
        FIRSTNAME: displayName || email.split('@')[0],
        REGISTRATION_DATE: new Date().toISOString().split('T')[0]
      },
      listIds: finalListIds,
      updateEnabled: true
    };

    // Make the API request to Brevo
    console.log('Sending request to Brevo API...');
    const response = await fetch('https://api.brevo.com/v3/contacts', {
      method: 'POST',
      headers: brevoApiHeaders,
      body: JSON.stringify(payload)
    });

    console.log('Brevo API response status:', response.status);
    
    // Check if this is a new contact (201) or an existing one (204)
    const isNewContact = response.status === 201;
    
    // Handle 201 Created or 204 No Content (contact updated)
    if (response.status === 201 || response.status === 204) {
      console.log(`Contact ${email} ${isNewContact ? 'created' : 'updated'} in Brevo and added to list ID ${DEFAULT_LIST_ID}`);
      return true;
    }

    const errorData = await response.json();
    console.error('Brevo API error data:', errorData);
    throw new Error(`Brevo API error: ${JSON.stringify(errorData)}`);
  } catch (error) {
    console.error('Error creating/updating contact:', error);
    return false;
  }
};

/**
 * Direct function to add a contact to a specific list
 * This is an alternative method that directly adds to a list
 */
export const addContactToList = async (email: string, listId: number = DEFAULT_LIST_ID): Promise<boolean> => {
  try {
    if (!apiKey) {
      console.error('Brevo API key is not configured');
      return false;
    }

    console.log(`Attempting to add ${email} directly to list ${listId}...`);
    
    // Make the API request to Brevo to add contact to list
    const response = await fetch(`https://api.brevo.com/v3/contacts/lists/${listId}/contacts/add`, {
      method: 'POST',
      headers: brevoApiHeaders,
      body: JSON.stringify({
        emails: [email]
      })
    });

    console.log('Add to list response status:', response.status);
    
    if (response.ok) {
      console.log(`Successfully added ${email} to list ${listId}`);
      return true;
    }

    const errorData = await response.json();
    console.error('Add to list error data:', errorData);
    throw new Error(`Add to list error: ${JSON.stringify(errorData)}`);
  } catch (error) {
    console.error('Error adding contact to list:', error);
    return false;
  }
};

// Also expose the direct list addition function
if (typeof window !== 'undefined') {
  (window as any).addContactToList = addContactToList;
  (window as any).TEMPLATE_ID = WELCOME_TEMPLATE_ID; // Expose template ID for testing
}

/**
 * Sends a welcome email to a single user using transactional email API
 * @param email - User's email address
 * @param displayName - User's display name
 * @returns Promise that resolves when the email has been sent
 */
export const sendWelcomeEmail = async (email: string, displayName: string): Promise<boolean> => {
  try {
    if (!apiKey) {
      console.error('Brevo API key is not configured');
      return false;
    }

    // First create or update the contact in Brevo
    // Add contact to list ID 6, but don't trigger any automations
    const contactCreated = await createOrUpdateContact(email, displayName);
    
    if (!contactCreated) {
      console.error('Failed to create/update contact. Trying alternative method...');
      // Try the alternative method to add contact to list
      await addContactToList(email);
    }

    // Create the transactional email payload
    const payload = {
      to: [{ email: email, name: displayName || email.split('@')[0] }],
      templateId: WELCOME_TEMPLATE_ID,
      params: {
        FIRSTNAME: displayName || email.split('@')[0],
        REGISTRATION_DATE: new Date().toISOString().split('T')[0]
      },
      // Add a unique tag to prevent duplicate sends
      tags: ["welcome_email", `user_${email.replace(/[^a-zA-Z0-9]/g, '_')}`]
    };

    // Make the API request to Brevo to send a transactional email
    console.log('Attempting to send transactional email...');
    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: brevoApiHeaders,
      body: JSON.stringify(payload)
    });

    console.log('Transactional email API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Transactional email API error data:', errorData);
      throw new Error(`Brevo API error: ${JSON.stringify(errorData)}`);
    }

    console.log(`Welcome email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
}; 