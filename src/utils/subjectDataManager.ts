// Supabase subject data management
import { useSupabaseSubjectStore } from '../stores/supabaseSubjectStore';
import { Subject } from '@/components/productivity/SubjectManager';

/**
 * Fetches subjects for a user from Supabase or cache.
 * @param userId The ID of the user.
 * @param forceRefresh Whether to bypass the cache.
 * @returns Array of subjects.
 */
export const getSubjects = async (userId: string, forceRefresh: boolean = false): Promise<Subject[]> => {
  const store = useSupabaseSubjectStore.getState();

  // Use the store's fetchSubjects method which handles caching and Supabase calls
  const supabaseSubjects = await store.fetchSubjects(userId, forceRefresh);

  // Convert Supabase subjects to the expected Subject format for compatibility
  const subjects: Subject[] = supabaseSubjects.map(subject => ({
    id: subject.id,
    name: subject.name,
    color: subject.color,
  }));

  return subjects;
};

/**
 * Adds a new subject to Supabase and updates the cache.
 * @param userId The ID of the user.
 * @param newSubject The subject to add.
 * @returns True if successful, false otherwise.
 */
export const addSubjectToSupabase = async (userId: string, newSubject: Subject): Promise<boolean> => {
  const store = useSupabaseSubjectStore.getState();

  // Check if subject already exists
  if (store.subjects.some(s => s.name.toLowerCase() === newSubject.name.toLowerCase())) {
    return false;
  }

  try {
    await store.addSubject(userId, newSubject.name, newSubject.color);
    return true;
  } catch (error) {
    console.error('Error adding subject to Supabase:', error);
    return false;
  }
};

/**
 * Updates an existing subject in Supabase and cache.
 * @param userId The ID of the user.
 * @param updatedSubject The subject with updated information.
 * @returns True if successful, false otherwise.
 */
export const updateSubjectInSupabase = async (userId: string, updatedSubject: Subject): Promise<boolean> => {
  const store = useSupabaseSubjectStore.getState();

  try {
    await store.updateSubject(updatedSubject.id, {
      name: updatedSubject.name,
      color: updatedSubject.color,
    });
    return true;
  } catch (error) {
    console.error('Error updating subject in Supabase:', error);
    return false;
  }
};

/**
 * Deletes a subject from Supabase and cache.
 * @param userId The ID of the user.
 * @param subjectId The ID of the subject to delete.
 * @returns True if successful, false otherwise.
 */
export const deleteSubjectInSupabase = async (userId: string, subjectId: string): Promise<boolean> => {
  const store = useSupabaseSubjectStore.getState();

  try {
    await store.deleteSubject(subjectId);
    return true;
  } catch (error) {
    console.error('Error deleting subject in Supabase:', error);
    return false;
  }
};

// Legacy function names for backward compatibility
export const addSubjectToFirestore = addSubjectToSupabase;
export const updateSubjectInFirestore = updateSubjectInSupabase;
export const deleteSubjectInFirestore = deleteSubjectInSupabase;
