// Utility functions for study sessions

/**
 * Returns an emoji representation for a given task type
 */
export const getTaskTypeEmoji = (taskType: string): string => {
  switch (taskType.toLowerCase()) {
    case "lecture":
      return "🎓"; // Graduation cap for lectures
    case "reading":
      return "📚"; // Books for reading
    case "exercise":
      return "✏️"; // Pencil for exercises 
    case "practice":
      return "💪"; // Strong arm for practice
    case "review":
      return "🔍"; // Magnifying glass for review
    case "homework":
      return "📝"; // Memo for homework
    case "test":
      return "📊"; // Chart for tests
    case "project":
      return "🛠️"; // Tools for projects
    case "research":
      return "🔬"; // Microscope for research
    case "custom":
      return "🎯"; // Target for custom
    case "study":
    default:
      return "📖"; // Open book for general study
  }
}; 