export const convertImageToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

// Cloudinary configuration
const CLOUDINARY_CLOUD_NAME = import.meta.env.VITE_CLOUDINARY_CLOUD_NAME || '';
const CLOUDINARY_UPLOAD_PRESET = import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET || '';
const CLOUDINARY_UPLOAD_URL = `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/upload`;

interface CloudinaryUploadResponse {
  secure_url: string;
  public_id: string;
  format: string;
  width: number;
  height: number;
  resource_type: string;
  created_at: string;
  bytes: number;
  original_filename: string;
  url: string;
  asset_id: string;
}

interface CloudinaryUploadOptions {
  folder?: string;
  tags?: string[];
  /**
   * @deprecated Transformation is not allowed in unsigned uploads.
   * Use upload_preset to apply transformations instead.
   */
  transformation?: string;
  maxFileSize?: number; // in bytes
  maxWidth?: number;
  maxHeight?: number;
}

/**
 * Uploads an image to Cloudinary and returns the response with image URL and metadata
 * @param file The image file to upload
 * @param options Optional upload configurations
 * @returns Promise with Cloudinary response including the secure URL
 */
export const uploadImageToCloudinary = async (
  file: File,
  options: CloudinaryUploadOptions = {}
): Promise<CloudinaryUploadResponse> => {
  // Check for empty files
  if (file.size === 0) {
    const error = 'Cannot upload an empty file to Cloudinary';
    console.error(error);
    throw new Error(error);
  }

  // Validate file size if specified
  if (options.maxFileSize && file.size > options.maxFileSize) {
    const error = `File size exceeds the maximum limit of ${options.maxFileSize / (1024 * 1024)}MB`;
    console.error(error);
    throw new Error(error);
  }

  // Prepare form data
  const formData = new FormData();
  formData.append('file', file);
  formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);

  // Add optional parameters if provided
  if (options.folder) {
    formData.append('folder', options.folder);
  }
  if (options.tags && options.tags.length > 0) {
    formData.append('tags', options.tags.join(','));
  }

  try {
    const response = await fetch(CLOUDINARY_UPLOAD_URL, {
      method: 'POST',
      body: formData,
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('Cloudinary error response:', responseData);
      throw new Error(responseData.error?.message || 'Failed to upload image to Cloudinary');
    }

    return responseData;
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw error;
  }
};

/**
 * Generates a Cloudinary image URL with transformations
 * @param publicId The public ID of the image
 * @param options Transformation options
 * @returns Cloudinary transformed image URL
 */
export const getCloudinaryImageUrl = (
  publicId: string,
  options: {
    width?: number;
    height?: number;
    crop?: 'fill' | 'scale' | 'fit' | 'thumb' | 'crop';
    quality?: number;
    format?: 'jpg' | 'png' | 'webp' | 'auto';
  } = {}
): string => {
  if (!publicId) return '';

  // Build transformation string
  const transformations = [];

  if (options.width) transformations.push(`w_${options.width}`);
  if (options.height) transformations.push(`h_${options.height}`);
  if (options.crop) transformations.push(`c_${options.crop}`);
  if (options.quality) transformations.push(`q_${options.quality}`);
  if (options.format) transformations.push(`f_${options.format}`);

  const transformationString = transformations.length > 0
    ? transformations.join(',') + '/'
    : '';

  return `https://res.cloudinary.com/${CLOUDINARY_CLOUD_NAME}/image/upload/${transformationString}${publicId}`;
};

/**
 * Deletes an image from Cloudinary (Note: This requires API Key and Secret, so it should be done server-side)
 * @param publicId The public ID of the image to delete
 * @returns Promise with deletion status
 */
export const deleteCloudinaryImage = async (publicId: string): Promise<boolean> => {
  // This should be implemented on your backend as it requires API secret
  // Frontend implementation would expose your API secret
  const endpoint = `/api/cloudinary/delete/${encodeURIComponent(publicId)}`;

  try {
    const response = await fetch(endpoint, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete image from Cloudinary');
    }

    return true;
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    throw error;
  }
};