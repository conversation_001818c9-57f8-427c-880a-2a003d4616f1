import { supabase } from '../integrations/supabase/client';
import { User } from '@supabase/supabase-js';

// Authentication utilities
// export const signInWithSupabase = async (user: User) => {
//   try {
//     // Create or update user in Supabase
//     const { data, error } = await supabase.auth.signInWithIdToken({
//       token: await user.getIdToken(),
//     });

//     if (error) {
//       console.error('Supabase sign in error:', error);
//       throw error;
//     }

//     return data;
//   } catch (error) {
//     console.error('Error signing in to Supabase:', error);
//     throw error;
//   }
// };

// AI Chats operations
export const getAIChatsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('aiChats')
    .select('*')
    .eq('userId', userId)
    .order('updated_at', { ascending: false });

  if (error) {
    console.error('Error fetching AI chats from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveAIChatToSupabase = async (chatData: any) => {
  const { data, error } = await supabase
    .from('aiChats')
    .upsert(chatData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving AI chat to Supabase:', error);
    throw error;
  }

  return data;
};

// Groups operations
export const getUserGroupsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('groups')
    .select('*')
    .or(`createdBy.eq.${userId},owner_id.eq.${userId},members.cs.{${userId}}`)
    .order('createdAt', { ascending: false });

  if (error) {
    console.error('Error fetching groups from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveGroupToSupabase = async (groupData: any) => {
  const { data, error } = await supabase
    .from('groups')
    .upsert(groupData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving group to Supabase:', error);
    throw error;
  }

  return data;
};

// Messages operations (commented out due to schema mismatch)
// export const getGroupMessagesFromSupabase = async (groupId: string) => {
//   const { data, error } = await supabase
//     .from('messages')
//     .select('*')
//     .eq('group_id', groupId)
//     .order('created_at', { ascending: true });

//   if (error) {
//     console.error('Error fetching messages from Supabase:', error);
//     throw error;
//   }

//   return data;
// };

// export const saveMessageToSupabase = async (messageData: any) => {
//   const { data, error } = await supabase
//     .from('messages')
//     .insert(messageData)
//     .select()
//     .single();

//   if (error) {
//     console.error('Error saving message to Supabase:', error);
//     throw error;
//   }

//   return data;
// };

// Todos operations
export const getUserTodosFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('todos')
    .select('*')
    .eq('created_by', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching todos from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveTodoToSupabase = async (todoData: any) => {
  const { data, error } = await supabase
    .from('todos')
    .upsert(todoData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving todo to Supabase:', error);
    throw error;
  }

  return data;
};

// User Subjects operations
export const getUserSubjectsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('userSubjects')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching subjects from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveSubjectToSupabase = async (subjectData: any) => {
  const { data, error } = await supabase
    .from('userSubjects')
    .upsert(subjectData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving subject to Supabase:', error);
    throw error;
  }

  return data;
};

// Exams operations
export const getUserExamsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('exams')
    .select('*')
    .eq('user_id', userId)
    .order('date', { ascending: true });

  if (error) {
    console.error('Error fetching exams from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveExamToSupabase = async (examData: any) => {
  const { data, error } = await supabase
    .from('exams')
    .upsert(examData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving exam to Supabase:', error);
    throw error;
  }

  return data;
};

// Study Sessions operations
export const getUserStudySessionsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('study_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('start_time', { ascending: false });

  if (error) {
    console.error('Error fetching study sessions from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveStudySessionToSupabase = async (sessionData: any) => {
  const { data, error } = await supabase
    .from('study_sessions')
    .upsert(sessionData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving study session to Supabase:', error);
    throw error;
  }

  return data;
};

// Mock Tests operations
export const getUserMockTestsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('mock_tests')
    .select('*')
    .eq('user_id', userId)
    .order('test_date', { ascending: false });

  if (error) {
    console.error('Error fetching mock tests from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveMockTestToSupabase = async (testData: any) => {
  const { data, error } = await supabase
    .from('mock_tests')
    .upsert(testData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving mock test to Supabase:', error);
    throw error;
  }

  return data;
};

// User Profile operations
export const getUserProfileFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
    console.error('Error fetching user profile from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveUserProfileToSupabase = async (userData: any) => {
  const { data, error } = await supabase
    .from('users')
    .upsert(userData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving user profile to Supabase:', error);
    throw error;
  }

  return data;
};

// Chat Comments operations (commented out due to schema mismatch)
// export const getChatCommentsFromSupabase = async (chatId: string) => {
//   const { data, error } = await supabase
//     .from('chatComments')
//     .select('*')
//     .eq('chat_id', chatId)
//     .order('created_at', { ascending: true });

//   if (error) {
//     console.error('Error fetching chat comments from Supabase:', error);
//     throw error;
//   }

//   return data;
// };

// export const saveChatCommentToSupabase = async (commentData: any) => {
//   const { data, error } = await supabase
//     .from('chatComments')
//     .insert(commentData)
//     .select()
//     .single();

//   if (error) {
//     console.error('Error saving chat comment to Supabase:', error);
//     throw error;
//   }

//   return data;
// };

// Real-time subscriptions
export const subscribeToUserData = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-data-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'users',
        filter: `id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToGroupMessages = (groupId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`group-${groupId}-messages`)
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'messages',
        filter: `group_id=eq.${groupId}`
      },
      callback
    )
    .subscribe();
};

// Additional AI Chat operations for compatibility with AI.tsx
export const deleteAIChatFromSupabase = async (chatId: string) => {
  const { error } = await supabase
    .from('aiChats')
    .delete()
    .eq('id', chatId);

  if (error) {
    console.error('Error deleting AI chat from Supabase:', error);
    throw error;
  }

  return true;
};

export const updateAIChatInSupabase = async (chatId: string, updates: any) => {
  const { data, error } = await supabase
    .from('aiChats')
    .update(updates)
    .eq('id', chatId)
    .select()
    .single();

  if (error) {
    console.error('Error updating AI chat in Supabase:', error);
    throw error;
  }

  return data;
};

// Chat history functions for AI.tsx compatibility
export const getUserChatHistory = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('aiChats')
      .select('*')
      .eq('userId', userId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching user chat history:', error);
      throw error;
    }

    // Convert Supabase format to expected ChatHistory format
    return data?.map((chat: any) => ({
      id: chat.id,
      timestamp: new Date(chat.created_at).getTime(),
      messages: chat.messages || [],
      preview: chat.preview || '',
      isStarred: chat.isStarred || false,
      isPinned: chat.isPinned || false,
      comments: [] // Comments are handled separately in discussions table
    })) || [];
  } catch (error) {
    console.error('Error in getUserChatHistory:', error);
    throw error;
  }
};

export const saveAIChat = async (chatId: string, chatData: any, isNew: boolean = false) => {
  try {
    const supabaseData = {
      id: chatId,
      userId: chatData.userId,
      messages: chatData.messages,
      preview: chatData.preview,
      isStarred: chatData.isStarred || false,
      isPinned: chatData.isPinned || false,
      created_at: isNew ? new Date().toISOString() : undefined,
      updated_at: new Date().toISOString()
    };

    // Remove undefined values
    Object.keys(supabaseData).forEach(key => {
      if (supabaseData[key] === undefined) {
        delete supabaseData[key];
      }
    });

    const { data, error } = await supabase
      .from('aiChats')
      .upsert(supabaseData, { onConflict: 'id' })
      .select()
      .single();

    if (error) {
      console.error('Error saving AI chat:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in saveAIChat:', error);
    throw error;
  }
};

export const deleteAIChat = async (chatId: string) => {
  return deleteAIChatFromSupabase(chatId);
};

export const toggleChatStarred = async (chatId: string, isStarred: boolean) => {
  return updateAIChatInSupabase(chatId, { isStarred: isStarred });
};

export const toggleChatPinned = async (chatId: string, isPinned: boolean) => {
  return updateAIChatInSupabase(chatId, { isPinned: isPinned });
};

export const incrementQuestionsAsked = async (userId: string) => {
  try {
    // Fetch the current user to get their latest user_metadata
    const { data: { user }, error: fetchError } = await supabase.auth.getUser();

    if (fetchError || !user) {
      console.error('Error fetching current user for incrementQuestionsAsked:', fetchError);
      throw fetchError;
    }

    const currentMetadata = user.user_metadata || {};
    const currentQuestionsAsked = (currentMetadata.questions_asked as number) || 0;

    const { data, error: updateError } = await supabase.auth.updateUser({
      data: {
        ...currentMetadata,
        questions_asked: currentQuestionsAsked + 1,
      },
    });

    if (updateError) {
      console.error('Error updating questions asked count:', updateError);
      throw updateError;
    }

    return true;
  } catch (error) {
    console.error('Error in incrementQuestionsAsked:', error);
    throw error;
  }
};

export const updateUserProfile = async (userId: string, updates: any) => {
  try {
    // Fetch the current user to get their latest user_metadata
    const { data: { user }, error: fetchError } = await supabase.auth.getUser();

    if (fetchError || !user) {
      console.error('Error fetching current user for updateUserProfile:', fetchError);
      throw fetchError;
    }

    const currentMetadata = user.user_metadata || {};
    const newMetadata = { ...currentMetadata, ...updates };

    const { data, error } = await supabase.auth.updateUser({
      data: newMetadata,
    });

    if (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    throw error;
  }
};
