import { useEffect } from 'react';

/**
 * Hook to set document title
 * @param title The title to set for the document
 * @param restoreOnUnmount Whether to restore the original title when the component unmounts (default: false)
 */
export const useDocumentTitle = (title: string) => {
  useEffect(() => {
    // Set the document title when the component mounts
    const originalTitle = document.title;
    document.title = title;
    
    // Restore the original title when the component unmounts
    return () => {
      document.title = originalTitle;
    };
  }, [title]);
};

export default useDocumentTitle; 