import { useState } from 'react';

// Popup is disabled - keeping the constants for reference
// const POPUP_DELAY = 60000;      // Show first popup after 1 minute
// const POPUP_INTERVAL = 86400000; // Repeat once per day (24 hours)
// const LAST_POPUP_KEY = 'lastSharePopup';

export const useSharePopup = () => {
  const [isOpen, setIsOpen] = useState(false);

  // No useEffect hook - popup will never show automatically

  const closePopup = () => {
    setIsOpen(false);
  };

  return {
    isOpen,
    closePopup
  };
};