<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-C823RR97DM"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-C823RR97DM');
  </script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Script Diagnostic</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .script-container {
      margin-bottom: 20px;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    button {
      padding: 8px 16px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    .log {
      margin-top: 10px;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
  <!-- Google AdSense -->
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9602732057654649"
   crossorigin="anonymous"></script>
</head>
<body>
  <h1>Script Diagnostic Tool</h1>
  <p>This tool helps identify which third-party script might be loading suspicious URLs.</p>
  
  <div class="script-container">
    <h2>1. GPT Engineer Script</h2>
    <button id="load-gpt">Load Script</button>
    <button id="unload-gpt">Unload</button>
    <div class="log" id="gpt-log"></div>
    <div id="gpt-script-container"></div>
  </div>
  
  <div class="script-container">
    <h2>2. Cloudflare Insights</h2>
    <button id="load-cf">Load Script</button>
    <button id="unload-cf">Unload</button>
    <div class="log" id="cf-log"></div>
    <div id="cf-script-container"></div>
  </div>
  
  <div class="script-container">
    <h2>3. Featurebase SDK</h2>
    <button id="load-fb">Load Script</button>
    <button id="unload-fb">Unload</button>
    <div class="log" id="fb-log"></div>
    <div id="fb-script-container"></div>
  </div>
  
  <script>
    // Function to monitor network requests
    function monitorNetwork(logElement) {
      const originalFetch = window.fetch;
      const originalXHR = window.XMLHttpRequest.prototype.open;
      
      // Monitor fetch requests
      window.fetch = function() {
        const url = arguments[0];
        if (typeof url === 'string') {
          logRequest(url, logElement);
        } else if (url instanceof Request) {
          logRequest(url.url, logElement);
        }
        return originalFetch.apply(this, arguments);
      };
      
      // Monitor XHR requests
      window.XMLHttpRequest.prototype.open = function() {
        const url = arguments[1];
        logRequest(url, logElement);
        return originalXHR.apply(this, arguments);
      };
      
      // Log the request
      function logRequest(url, logElement) {
        if (url.includes('fywiei.com') || url.includes('jr.php')) {
          const entry = document.createElement('div');
          entry.style.color = 'red';
          entry.style.fontWeight = 'bold';
          entry.textContent = `⚠️ SUSPICIOUS: ${url}`;
          logElement.appendChild(entry);
        } else {
          const entry = document.createElement('div');
          entry.textContent = url;
          logElement.appendChild(entry);
        }
        logElement.scrollTop = logElement.scrollHeight;
      }
    }
    
    // Function to load a script
    function loadScript(src, containerId, logId) {
      const container = document.getElementById(containerId);
      const logElement = document.getElementById(logId);
      
      // Clear previous content
      container.innerHTML = '';
      logElement.innerHTML = '';
      
      // Start monitoring network
      monitorNetwork(logElement);
      
      // Create and append script
      const script = document.createElement('script');
      script.src = src;
      script.async = true;
      
      script.onload = function() {
        logElement.innerHTML += '<div style="color: green;">Script loaded successfully</div>';
      };
      
      script.onerror = function() {
        logElement.innerHTML += '<div style="color: red;">Error loading script</div>';
      };
      
      container.appendChild(script);
      logElement.innerHTML += `<div>Loading script: ${src}</div>`;
    }
    
    // Function to unload scripts
    function unloadScript(containerId, logId) {
      const container = document.getElementById(containerId);
      const logElement = document.getElementById(logId);
      
      container.innerHTML = '';
      logElement.innerHTML += '<div>Script unloaded</div>';
    }
    
    // Set up event listeners
    document.getElementById('load-gpt').addEventListener('click', function() {
      loadScript('https://cdn.gpteng.co/gptengineer.js', 'gpt-script-container', 'gpt-log');
    });
    
    document.getElementById('unload-gpt').addEventListener('click', function() {
      unloadScript('gpt-script-container', 'gpt-log');
    });
    
    document.getElementById('load-cf').addEventListener('click', function() {
      loadScript('https://static.cloudflareinsights.com/beacon.min.js', 'cf-script-container', 'cf-log');
    });
    
    document.getElementById('unload-cf').addEventListener('click', function() {
      unloadScript('cf-script-container', 'cf-log');
    });
    
    document.getElementById('load-fb').addEventListener('click', function() {
      loadScript('https://do.featurebase.app/js/sdk.js', 'fb-script-container', 'fb-log');
    });
    
    document.getElementById('unload-fb').addEventListener('click', function() {
      unloadScript('fb-script-container', 'fb-log');
    });
  </script>
</body>
</html>